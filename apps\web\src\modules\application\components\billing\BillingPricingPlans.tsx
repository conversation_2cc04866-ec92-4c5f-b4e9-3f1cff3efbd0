import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardDescription,
    Badge
} from '@repo/ui';
import { Tb<PERSON><PERSON><PERSON>, TbCrown } from 'react-icons/tb';

interface PricingTier {
    id: string;
    name: string;
    description: string;
    monthly_price: number;
    annual_price: number;
    features: string[];
    highlighted?: boolean;
    cta: string;
    disabled?: boolean;
}

interface BillingPricingPlansProps {
    subscription: any | null;
    isProcessing: boolean;
    onSubscribe: () => Promise<void>;
    billingPeriod: 'monthly' | 'annual';
}

// Define pricing tiers
const pricingTiers: PricingTier[] = [
    {
        id: 'free',
        name: 'Free',
        description: 'Basic features for personal use',
        monthly_price: 0,
        annual_price: 0,
        features: [
            'Up to 5 decks',
            'Basic flashcard types',
            'Standard spaced repetition',
            '100MB storage',
            'Community support'
        ],
        cta: 'Current Plan',
        disabled: true
    },
    {
        id: 'pro',
        name: 'Pro',
        description: 'Advanced features for power users',
        monthly_price: 19.99,
        annual_price: 149.99,
        features: [
            'Unlimited decks',
            'All flashcard types',
            'Advanced FSRS algorithm',
            '10GB storage',
            'Unlimited documents',
            'Advanced analytics',
            'AI-powered learning tools',
            'Priority support',
            'Early access to new features'
        ],
        highlighted: true,
        cta: 'Upgrade to Pro'
    }
];

export default function BillingPricingPlans({
    subscription,
    isProcessing,
    onSubscribe,
    billingPeriod
}: BillingPricingPlansProps) {
    return (
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            {/* Free Plan */}
            <Card>
                <CardHeader>
                    <CardTitle>Free Plan</CardTitle>
                    <CardDescription>Basic features for personal use</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className='mb-4'>
                        <span className='text-3xl font-bold'>$0</span>
                        <span className='ml-1 text-gray-500'>/month</span>
                    </div>
                    <ul className='space-y-2'>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Up to 5 decks</span>
                        </li>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Basic flashcard types</span>
                        </li>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Standard spaced repetition</span>
                        </li>
                    </ul>
                </CardContent>
                <CardFooter>
                    <Button variant='outline' disabled className='w-full'>
                        Current Plan
                    </Button>
                </CardFooter>
            </Card>

            {/* Pro Plan */}
            <Card className='border-amber-200 shadow-md'>
                <CardHeader className='rounded-t-lg bg-gradient-to-r from-amber-50 to-amber-100'>
                    <div className='flex items-center justify-between'>
                        <CardTitle>Pro Plan</CardTitle>
                        <Badge className='bg-amber-500 hover:bg-amber-600'>
                            <TbCrown className='mr-1 h-3.5 w-3.5' />
                            Recommended
                        </Badge>
                    </div>
                    <CardDescription>Advanced features for power users</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className='mb-4'>
                        <span className='text-3xl font-bold'>
                            ${billingPeriod === 'monthly' ? '19.99' : '149.99'}
                        </span>
                        <span className='ml-1 text-gray-500'>
                            /{billingPeriod === 'monthly' ? 'month' : 'year'}
                        </span>
                    </div>
                    <ul className='space-y-2'>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Unlimited decks</span>
                        </li>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>All flashcard types</span>
                        </li>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Advanced FSRS algorithm</span>
                        </li>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Priority support</span>
                        </li>
                        <li className='flex items-center'>
                            <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                            <span className='text-sm'>Advanced analytics</span>
                        </li>
                    </ul>
                </CardContent>
                <CardFooter>
                    {subscription?.status === 'active' ? (
                        <Button variant='outline' disabled className='w-full'>
                            Current Plan
                        </Button>
                    ) : (
                        <Button
                            className='w-full bg-amber-500 hover:bg-amber-600'
                            onClick={onSubscribe}
                            disabled={isProcessing}
                        >
                            {isProcessing ? 'Processing...' : 'Upgrade to Pro'}
                        </Button>
                    )}
                </CardFooter>
            </Card>
        </div>
    );
}
