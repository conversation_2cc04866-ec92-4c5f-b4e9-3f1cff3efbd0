import React, { useEffect } from 'react';
import { <PERSON>, Button } from '@repo/ui';
import { <PERSON><PERSON><PERSON>, FolderKanban, RefreshCw, Search } from 'lucide-react';
import { XYPosition, useNodesData, useReactFlow } from '@xyflow/react';
import { useApiStore } from '@repo/api';

export interface ContextMenuData {
    top?: number;
    left?: number;
    right?: number;
    bottom?: number;
    type: 'pane' | 'node' | 'edge' | null;
    ids?: string[];
    position?: XYPosition;
}

export interface ContextMenuActions {
    onCreateTopicGroup?: (nodeIds: string[], position?: XYPosition) => void;
    onCreateSubjectGroup?: (nodeIds: string[], position?: XYPosition) => void;
    onGenerateContent?: (nodeIds: string[]) => void;
    onUpdateFlashcards?: (nodeIds: string[]) => void;
}

export interface ContextMenuProps {
    menuData: ContextMenuData;
    onClose: () => void;
}

export function ContextMenu({
    menuData,
    onClose,
    onCreateTopicGroup,
    onCreateSubjectGroup,
    onGenerateContent,
    onUpdateFlashcards
}: ContextMenuProps & ContextMenuActions) {
    const { top, left, right, bottom, type, ids } = menuData;
    const nodeIds = type === 'node' ? ids || [] : [];
    const nodes = useNodesData(nodeIds);
    const reactFlowInstance = useReactFlow();
    const selectedNodeTitle = nodes[0]?.data?.conceptTitle || '';

    // Position styles
    const style: React.CSSProperties = {
        position: 'fixed', // Use fixed instead of absolute for more reliable positioning
        zIndex: 9999,
        ...(top !== undefined ? { top: `${top}px` } : {}),
        ...(left !== undefined ? { left: `${left}px` } : {}),
        ...(right !== undefined ? { right: `${right}px` } : {}),
        ...(bottom !== undefined ? { bottom: `${bottom}px` } : {})
    };

    // Handle clicks and prevent propagation
    const handleClick = (callback?: Function, args?: any[]) => (e: React.MouseEvent) => {
        e.stopPropagation();
        if (callback) {
            callback(...(args || []));
        }
        onClose();
    };

    // Add a global click handler to close the menu when clicking outside
    useEffect(() => {
        const handleGlobalClick = (event: MouseEvent) => {
            // Check if the click target is outside the context menu itself
            const menuElement = document.getElementById('reactflow-context-menu'); // Assuming you add an ID to the menu card
            if (menuElement && !menuElement.contains(event.target as Node)) {
                onClose();
            }
        };

        // Delay adding listener slightly to prevent immediate closure on the same click that opened it
        const timerId = setTimeout(() => {
            document.addEventListener('click', handleGlobalClick);
            document.addEventListener('contextmenu', handleGlobalClick); // Close on subsequent right-clicks too
        }, 0);

        // Clean up the event listener when component unmounts or menuData changes
        return () => {
            clearTimeout(timerId);
            document.removeEventListener('click', handleGlobalClick);
            document.removeEventListener('contextmenu', handleGlobalClick);
        };
    }, [onClose, menuData]); // Re-attach listener if menuData changes (menu reopens)

    // Prevent the menu from closing when clicking inside it
    const handleMenuClick = (e: React.MouseEvent) => {
        e.stopPropagation();
    };

    return (
        <Card
            id='reactflow-context-menu' // Added ID for useEffect cleanup
            className='pointer-events-auto fixed z-[9999] w-48 border border-gray-300 bg-white py-2 shadow-lg'
            style={style}
            onClick={handleMenuClick} // Prevent clicks inside menu from closing it via overlay
            onContextMenu={(e: React.MouseEvent) => {
                e.preventDefault(); // Prevent browser context menu on the menu itself
                e.stopPropagation();
            }}
        >
            <div className='flex flex-col gap-1'>
                {/* Pane Actions */}
                {type === 'pane' && (
                    <>
                        {onCreateTopicGroup && (
                            <Button
                                variant='ghost'
                                size='sm'
                                className='justify-start rounded-none px-3'
                                onClick={handleClick(onCreateTopicGroup, [[], menuData.position])}
                            >
                                <FolderKanban className='mr-2 h-4 w-4 text-blue-500' />
                                Create Topic Group
                            </Button>
                        )}

                        {onCreateSubjectGroup && (
                            <Button
                                variant='ghost'
                                size='sm'
                                className='justify-start rounded-none px-3'
                                onClick={handleClick(onCreateSubjectGroup, [[], menuData.position])}
                            >
                                <FolderKanban className='mr-2 h-4 w-4 text-purple-500' />
                                Create Subject Group
                            </Button>
                        )}
                    </>
                )}
                {/* Node Actions */}
                {type === 'node' && nodeIds.length > 0 && (
                    <>
                        <Button
                            variant='ghost'
                            size='sm'
                            className='justify-start rounded-none px-3'
                            onClick={handleClick(onGenerateContent, [nodeIds])}
                            disabled={!onGenerateContent}
                        >
                            <Sparkles className='mr-2 h-4 w-4' />
                            Generate Content
                        </Button>

                        {/* Only show Find Concepts for single node selection */}
                        {nodeIds.length === 1 && (
                            <Button
                                variant='ghost'
                                size='sm'
                                className='justify-start rounded-none px-3'
                                onClick={handleClick(() => {
                                    // Trigger finding concepts using the useApiStore
                                    const apiStore = useApiStore.getState();

                                    // Make sure we have a valid node title and ID
                                    if (!selectedNodeTitle || !nodeIds[0]) {
                                        console.error(
                                            '[ContextMenu] Missing node title or ID for Find Concepts'
                                        );
                                        return;
                                    }

                                    console.log(
                                        '[ContextMenu] Finding concepts for node:',
                                        nodeIds[0],
                                        'with title:',
                                        selectedNodeTitle
                                    );

                                    // Get all nodes from the current map to extract existing concept titles
                                    const allNodes = reactFlowInstance.getNodes();
                                    const existingConceptTitles = allNodes
                                        .filter(
                                            node =>
                                                // Filter out the current node and ensure nodes have conceptTitle
                                                node.id !== nodeIds[0] &&
                                                node.data?.conceptTitle &&
                                                typeof node.data.conceptTitle === 'string' &&
                                                node.data.conceptTitle.trim() !== ''
                                        )
                                        .map(node => String(node.data.conceptTitle));

                                    console.log(
                                        '[ContextMenu] Existing concept titles:',
                                        existingConceptTitles
                                    );

                                    // Get the source node's sections to provide better context
                                    const sourceNode = nodes[0];
                                    const sourceSections = sourceNode?.data?.sections || {};

                                    // Convert sections to the format expected by the API
                                    const apiSections: Record<string, any> = {};
                                    Object.entries(sourceSections).forEach(([key, section]) => {
                                        if (section && typeof section === 'object') {
                                            apiSections[key] = {
                                                id: section.id || key,
                                                label: section.label || key,
                                                icon: section.icon || '📄',
                                                value: section.value || ''
                                            };
                                        }
                                    });

                                    console.log('[ContextMenu] Source sections:', apiSections);

                                    // Set up the API store with the necessary data
                                    apiStore.setSourceConceptTitle(
                                        String(selectedNodeTitle || 'Untitled Concept')
                                    );
                                    apiStore.setExistingConceptTitles(existingConceptTitles);
                                    apiStore.setSourceSections(apiSections);
                                    apiStore.setRequestType('concepts');
                                    apiStore.setCurrentNodeId(nodeIds[0]);

                                    // Generate the concepts
                                    apiStore.generate();
                                })}
                            >
                                <Search className='mr-2 h-4 w-4' />
                                Find Concepts
                            </Button>
                        )}
                        {onCreateTopicGroup && (
                            <Button
                                variant='ghost'
                                size='sm'
                                className='justify-start rounded-none px-3'
                                onClick={handleClick(onCreateTopicGroup, [nodeIds])}
                            >
                                <FolderKanban className='mr-2 h-4 w-4 text-blue-500' />
                                Add to Topic Group
                            </Button>
                        )}

                        {onCreateSubjectGroup && (
                            <Button
                                variant='ghost'
                                size='sm'
                                className='justify-start rounded-none px-3'
                                onClick={handleClick(onCreateSubjectGroup, [nodeIds])}
                            >
                                <FolderKanban className='mr-2 h-4 w-4 text-purple-500' />
                                Add to Subject Group
                            </Button>
                        )}
                        {nodeIds.length > 0 && (
                            <Button
                                variant='ghost'
                                size='sm'
                                className='justify-start rounded-none px-3'
                                onClick={handleClick(
                                    onUpdateFlashcards ||
                                        (() => {
                                            console.log('No onUpdateFlashcards handler provided');
                                        }),
                                    [nodeIds]
                                )}
                            >
                                <RefreshCw className='mr-2 h-4 w-4' />
                                Update Flashcards
                            </Button>
                        )}
                        {/* Add Align actions if needed */}
                    </>
                )}
            </div>
        </Card>
    );
}
