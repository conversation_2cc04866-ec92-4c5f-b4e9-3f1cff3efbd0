{"name": "web", "private": true, "type": "module", "scripts": {"build": "run-s typecheck build:react-router build:netlify-prepare", "build:react-router": "react-router build", "build:netlify-prepare": "node netlify/prepare.js", "dev": "react-router dev", "start": "netlify serve", "test": "vitest run", "test:watch": "vitest --watch", "typecheck": "react-router typegen && tsc --build --noEmit", "lint": "run-s eslint stylelint", "eslint": "eslint '*/**/*.{js,ts,jsx,tsx}' --fix", "stylelint": "stylelint '**/*.{css,scss}' --fix"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@journeyapps/wa-sqlite": "^1.2.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@react-router/node": "^7.5.0", "@repo/db": "workspace:*", "@repo/utils": "workspace:*", "@repo/dashboard": "workspace:*", "@repo/ui": "workspace:*", "@repo/map": "workspace:*", "@xyflow/react": "^12.5.6", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^12.7.4", "isbot": "^5.1.17", "lucide-react": "^0.464.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.52.1", "react-router": "^7.5.0", "recharts": "^2.15.2", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@react-router/dev": "^7.5.0", "@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/review": "workspace:*", "@repo/map": "workspace:*", "@repo/utils": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/dashboard": "workspace:*", "@repo/vite-config": "workspace:*", "@tailwindcss/typography": "^0.5.13", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/express": "^5.0.0", "@types/node": "^20.14.11", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "autoprefixer": "^10.4.19", "express": "^4.21.1", "jsdom": "^25.0.1", "npm-run-all2": "^7.0.1", "postcss": "^8.4.39", "sass": "^1.81.0", "tailwindcss": "^3.4.6", "terser": "^5.36.0", "typescript": "^5.5.4", "vite": "^5.3.4", "vitest": "^2.1.8"}, "engines": {"node": ">=20.18.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}