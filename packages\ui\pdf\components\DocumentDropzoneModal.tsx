import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
    <PERSON>ton,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@repo/ui';
import { Loader2Icon, FileIcon, UploadIcon } from 'lucide-react';
import { cn } from '../../export';

interface DocumentDropzoneModalProps {
    children: React.ReactNode;
    isLoading?: boolean;
    onSubmit: (files: File[]) => Promise<boolean>;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
}

export function DocumentDropzoneModal({
    children,
    isLoading = false,
    onSubmit,
    open: controlledOpen,
    onOpenChange: setControlledOpen
}: Readonly<DocumentDropzoneModalProps>) {
    const [open, setOpen] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [files, setFiles] = useState<File[]>([]);

    // Use controlled open state if provided, otherwise use internal state
    const isOpen = controlledOpen !== undefined ? controlledOpen : open;
    const setIsOpen = setControlledOpen || setOpen;

    const onDrop = useCallback((acceptedFiles: File[]) => {
        setFiles(acceptedFiles);
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'application/pdf': ['.pdf'],
            'text/plain': ['.txt'],
            'text/markdown': ['.md'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
        }
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (files.length === 0) return;

        try {
            setSubmitting(true);
            const success = await onSubmit(files);

            if (success) {
                // Reset form and close modal
                setFiles([]);
                setIsOpen(false);
            }
        } catch (error) {
            console.error('Error uploading documents:', error);
        } finally {
            setSubmitting(false);
        }
    };

    // Disable the submit button if loading, submitting, or no files selected
    const submitDisabled = isLoading || submitting || files.length === 0;

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogContent className='overflow-hidden rounded-lg border border-gray-200 bg-white p-0 shadow-lg sm:max-w-[500px]'>
                <form onSubmit={handleSubmit}>
                    <DialogHeader className='px-6 pb-2 pt-6'>
                        <DialogTitle className='text-xl font-semibold text-gray-900'>
                            Add Document
                        </DialogTitle>
                        <DialogDescription className='mt-1 text-sm text-gray-500'>
                            Upload a document to your workspace. Supported formats: PDF, TXT, MD,
                            DOC, DOCX.
                        </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-5 px-6 py-4'>
                        <div
                            {...getRootProps()}
                            className={cn(
                                'flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-6 transition-colors',
                                isDragActive && 'border-blue-500 bg-blue-50',
                                (isLoading || submitting) && 'cursor-not-allowed opacity-50'
                            )}
                        >
                            <input {...getInputProps()} disabled={isLoading || submitting} />
                            <div className='flex flex-col items-center justify-center space-y-2 text-center'>
                                <UploadIcon className='h-10 w-10 text-gray-400' />
                                <p className='text-sm font-medium text-gray-700'>
                                    {isDragActive
                                        ? 'Drop the files here...'
                                        : 'Drag & drop files here, or click to select files'}
                                </p>
                                <p className='text-xs text-gray-500'>
                                    PDF, TXT, MD, DOC, DOCX up to 10MB
                                </p>
                            </div>
                        </div>

                        {files.length > 0 && (
                            <div className='mt-4 space-y-2'>
                                <p className='text-sm font-medium text-gray-700'>Selected files:</p>
                                <ul className='max-h-40 overflow-y-auto rounded-md border border-gray-200 bg-gray-50 p-2'>
                                    {files.map((file, index) => (
                                        <li
                                            key={index}
                                            className='flex items-center space-x-2 rounded-md p-2 text-sm text-gray-700'
                                        >
                                            <FileIcon className='h-4 w-4 text-gray-500' />
                                            <span className='flex-1 truncate'>{file.name}</span>
                                            <span className='text-xs text-gray-500'>
                                                {(file.size / 1024 / 1024).toFixed(2)} MB
                                            </span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                    <DialogFooter className='flex justify-end border-t border-gray-100 bg-gray-50 px-6 py-4'>
                        <Button
                            type='submit'
                            disabled={submitDisabled}
                            className='rounded-md bg-blue-600 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-700'
                        >
                            {submitting ? (
                                <>
                                    <Loader2Icon className='mr-2 h-4 w-4 animate-spin text-white' />
                                    Uploading...
                                </>
                            ) : (
                                'Upload Document'
                            )}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
