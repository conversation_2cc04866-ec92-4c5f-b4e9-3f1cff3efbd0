import React from 'react';
import { cn } from '@repo/ui';

interface ActionMenuProps {
  children: React.ReactNode;
  isOpen: boolean;
  className?: string;
  title?: string;
  description?: string;
  maxHeight?: string;
}

export function ActionMenu({
  children,
  isOpen,
  className,
  title,
  description,
  maxHeight = '500px'
}: ActionMenuProps) {
  return (
    <div
      style={{ maxHeight: isOpen ? maxHeight : '0' }}
      className={cn(
        'overflow-hidden transition-all duration-300 ease-in-out',
        isOpen ? 'py-1 opacity-100' : 'py-0 opacity-0',
        className
      )}
    >
      <div className='border-b border-gray-200 p-3'>
        {(title || description) && (
          <div className='mb-3'>
            {title && <h3 className='text-sm font-medium'>{title}</h3>}
            {description && <p className='text-xs text-gray-500'>{description}</p>}
          </div>
        )}
        {children}
      </div>
    </div>
  );
}
