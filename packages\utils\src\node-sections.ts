// Define the type for a node section
export type NodeSectionDefinition = {
    defaultTitle: string;
    icon: string;
    color: string;
    generationPrompt: string;
    temperature?: number;
    maxTokens?: number;
};

export const NODE_SECTIONS: Record<string, NodeSectionDefinition> = {
    /* ─────────── Core set ─────────── */
    coreIdea: {
        defaultTitle: 'Core Idea',
        icon: '💡',
        color: 'bg-blue-500/10 text-blue-400',
        generationPrompt:
            'In 3‑5 sentences: give a formal definition, list 2‑3 unique technical attributes that clearly separate the concept from its nearest related concepts, and state its relevancy (like why does this even exist in the first place). Include no anecdotes or filler.',
        temperature: 0.7,
        maxTokens: 500
    },
    specifications: {
        defaultTitle: 'Specifications',
        icon: '📐',
        color: 'bg-yellow-500/10 text-yellow-400',
        generationPrompt:
            'Bullet‑list every (technical if applicable) specification: parameter — exact units/range, constraints, required environment. Or, if not technically related, list in-depth specifications about the concept. Omit optional or contextual details.',
        temperature: 0.6,
        maxTokens: 600
    },
    mnemonic: {
        defaultTitle: 'Mnemonic',
        icon: '🧠',
        color: 'bg-amber-500/10 text-amber-400',
        generationPrompt:
            "Create one vivid mnemonic ≤120 chars encoding the concept's name + a key attribute via rhyme, acronym, or imagery. No explanation.",
        temperature: 0.8,
        maxTokens: 300
    },
    graph: {
        defaultTitle: 'Graph',
        icon: '📊',
        color: 'bg-sky-500/10 text-sky-400',
        generationPrompt:
            'Return only a Mermaid `graph TD` block linking the concept node to ≤6 major sub‑components. Use ≤3‑word node labels; no commentary.',
        temperature: 0.5,
        maxTokens: 400
    },
    picmonic: {
        defaultTitle: 'Picmonic',
        icon: '🖼️',
        color: 'bg-rose-500/10 text-rose-400',
        generationPrompt:
            "Describe one striking mental picture ≤120 chars weaving the concept's most distinctive elements into a single scene.",
        temperature: 0.8,
        maxTokens: 300
    },
    misconception: {
        defaultTitle: 'Misconception',
        icon: '❌',
        color: 'bg-cyan-500/10 text-cyan-400',
        generationPrompt:
            'State one prevalent misconception in ≤12 words, newline, then correct it in 1–2 precise technical sentences.',
        temperature: 0.7,
        maxTokens: 350
    },
    antiPattern: {
        defaultTitle: 'Anti‑Pattern',
        icon: '🚫',
        color: 'bg-red-500/10 text-red-400',
        generationPrompt:
            'Name a frequent anti‑pattern (≤8 words): explain in ≤2 sentences the technical failure it causes.',
        temperature: 0.7,
        maxTokens: 350
    },
    example: {
        defaultTitle: 'Example',
        icon: '📚',
        color: 'bg-green-500/10 text-green-400',
        generationPrompt:
            "Provide one eye-opening, illustrative example example (or counter-example) focused solely on the concept's core. If code, wrap in ```lang … ```; ≤20 lines, no boilerplate.",
        temperature: 0.6,
        maxTokens: 700
    },
    qa: {
        defaultTitle: 'Q/A',
        icon: '💬',
        color: 'bg-purple-500/10 text-purple-400',
        generationPrompt:
            'Q: <probing question>\nA: <2-sentence, technically rigorous answer>. No introduction.',
        temperature: 0.7,
        maxTokens: 400
    },
    pitfalls: {
        defaultTitle: 'Pitfall',
        icon: '⚠️',
        color: 'bg-red-500/10 text-red-400',
        generationPrompt:
            'Give one common implementation pitfall — and concise verb‑led avoidance tip in ≤2 sentences.',
        temperature: 0.7,
        maxTokens: 350
    },
    bestPractices: {
        defaultTitle: 'Best Practice',
        icon: '✅',
        color: 'bg-green-500/10 text-green-400',
        generationPrompt:
            'State one concrete best practice starting with an imperative verb, then specify a measurable benefit in ≤2 sentences.',
        temperature: 0.7,
        maxTokens: 350
    },
    comparison: {
        defaultTitle: 'Comparison',
        icon: '⚖️',
        color: 'bg-amber-500/10 text-amber-400',
        generationPrompt:
            'Bullet‑list exactly 3 pivotal technical differences vs. the nearest alternative. Start each bullet with "•".',
        temperature: 0.6,
        maxTokens: 500
    },
    analogy: {
        defaultTitle: 'Analogy',
        icon: '🔍',
        color: 'bg-pink-500/10 text-pink-400',
        generationPrompt:
            "Craft an everyday analogy in ≤2 sentences (≤150 chars) that parallels the concept's essence without oversimplifying.",
        temperature: 0.8,
        maxTokens: 300
    },
    formula: {
        defaultTitle: 'Formula',
        icon: '∑',
        color: 'bg-fuchsia-500/10 text-fuchsia-400',
        generationPrompt:
            'Present the principal equation in $$…$$ on its own line, then explain every symbol in one compact comma‑separated sentence.',
        temperature: 0.5,
        maxTokens: 400
    },
    stepByStep: {
        defaultTitle: 'Step‑by‑Step',
        icon: '🪜',
        color: 'bg-slate-500/10 text-slate-400',
        generationPrompt:
            'Ordered list of 3–6 canonical steps, each ≤12 words, all starting with a verb.',
        temperature: 0.6,
        maxTokens: 500
    },
    caseStudy: {
        defaultTitle: 'Case Study',
        icon: '🏷️',
        color: 'bg-rose-600/10 text-rose-500',
        generationPrompt:
            'Problem → concept intervention → outcome with at least one numeric metric; ≤3 sentences.',
        temperature: 0.7,
        maxTokens: 450
    },
    history: {
        defaultTitle: 'History',
        icon: '⌛',
        color: 'bg-zinc-500/10 text-zinc-400',
        generationPrompt:
            '2–3 chronological sentences: origin, landmark milestones (with years), current status.',
        temperature: 0.6,
        maxTokens: 400
    }
};
