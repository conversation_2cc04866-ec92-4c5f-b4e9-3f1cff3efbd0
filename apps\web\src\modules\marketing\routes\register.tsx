import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Link, useNavigate } from 'react-router';
import { useAuth } from '@repo/db';

import {
    Button,
    Input,
    Card,
    CardHeader,
    CardContent,
    CardFooter,
    Label,
    Form,
    FormField,
    FormItem,
    FormControl,
    FormMessage
} from '@repo/ui';

// Schema for registration form with validation
const registerSchema = z
    .object({
        email: z.string().email({ message: 'Invalid email address' }),
        password: z
            .string()
            .min(6, { message: 'Password must be at least 6 characters' })
            .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
                message:
                    'Password must contain at least one uppercase letter, one lowercase letter, and one number'
            }),
        confirmPassword: z.string().min(6, { message: 'Password must be at least 6 characters' })
    })
    .refine(data => data.password === data.confirmPassword, {
        message: "Passwords don't match",
        path: ['confirmPassword']
    });

type RegisterFormValues = z.infer<typeof registerSchema>;

export default function Register() {
    const navigate = useNavigate();
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const { signUp } = useAuth();

    // Initialize form with validation
    const form = useForm<RegisterFormValues>({
        resolver: zodResolver(registerSchema),
        defaultValues: {
            email: '',
            password: '',
            confirmPassword: ''
        }
    });

    // Form submission handler
    const onSubmit = async (values: RegisterFormValues) => {
        setLoading(true);
        setError(null);

        try {
            const { success, error: signUpError } = await signUp(
                values.email,
                values.password,
                values.email.split('@')[0]
            );

            if (!success) {
                setError(signUpError || 'Failed to register');
                setLoading(false);
                return;
            }

            // Successful registration - navigate to dashboard or login page
            console.log('[REGISTER] Sign up successful');
            navigate('/dashboard');
        } catch (err) {
            console.error('[REGISTER] Error during registration:', err);
            setError('An unexpected error occurred. Please try again.');
            setLoading(false);
        }
    };

    return (
        <div className='flex min-h-screen items-center justify-center'>
            <Card className='w-full max-w-md p-6'>
                <CardHeader>
                    <h1 className='text-2xl font-semibold'>Register</h1>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
                            <FormField
                                control={form.control}
                                name='email'
                                render={({ field }) => (
                                    <FormItem>
                                        <Label>Email</Label>
                                        <FormControl>
                                            <Input
                                                type='email'
                                                placeholder='<EMAIL>'
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name='password'
                                render={({ field }) => (
                                    <FormItem>
                                        <Label>Password</Label>
                                        <FormControl>
                                            <Input
                                                type='password'
                                                placeholder='********'
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name='confirmPassword'
                                render={({ field }) => (
                                    <FormItem>
                                        <Label>Confirm Password</Label>
                                        <FormControl>
                                            <Input
                                                type='password'
                                                placeholder='********'
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            {error && <p className='text-sm text-red-500'>{error}</p>}
                            <Button type='submit' disabled={loading} className='w-full'>
                                {loading ? 'Registering...' : 'Register'}
                            </Button>
                        </form>
                    </Form>
                </CardContent>
                <CardFooter className='flex justify-between'>
                    <p className='text-sm'>
                        Already have an account?{' '}
                        <Link to='/login' className='underline'>
                            Login
                        </Link>
                    </p>
                </CardFooter>
            </Card>
        </div>
    );
}
