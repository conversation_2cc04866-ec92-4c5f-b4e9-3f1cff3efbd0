{"extends": "@repo/typescript-config/react", "compilerOptions": {"types": ["react", "vite/client", "vite/types/importMeta.d.ts", "vitest/config", "vitest/globals", "vitest/jsdom", "@testing-library/jest-dom"], "rootDirs": [".", "./.react-router/types"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["vite.config.ts", "src", "netlify", "eslintrc.config.js", ".react-router/types/**/*", "server", "react-router.config.ts", "tailwind.config.ts"], "exclude": ["build", "node_modules", ".netlify", ".react-router", "public"]}