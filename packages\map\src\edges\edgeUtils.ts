import { Position, useReactFlow, InternalNode, XYPosition } from '@xyflow/react';
import { EDGE_TYPES } from '@repo/utils'; // Import constant definitions from utils package

// Helper function to get the next edge type in the cycle
export const getNextEdgeType = (currentTypeKey: string | undefined): keyof typeof EDGE_TYPES => {
    const edgeTypeKeys = Object.keys(EDGE_TYPES) as (keyof typeof EDGE_TYPES)[];
    const currentType =
        currentTypeKey && currentTypeKey in EDGE_TYPES ? currentTypeKey : 'relatedTo';
    const currentIndex = edgeTypeKeys.indexOf(currentType as keyof typeof EDGE_TYPES);
    const nextIndex = (currentIndex + 1) % edgeTypeKeys.length;
    return edgeTypeKeys[nextIndex];
};

// This function might be better placed within a hook (like useMapContextMenu)
// if it directly uses setEdges, but keeping it here as a utility for now.
// It requires setEdges to be passed in if used purely as a utility.
export const cycleEdgeType = (
    edgeId: string,
    setEdges: ReturnType<typeof useReactFlow>['setEdges']
) => {
    setEdges(eds =>
        eds.map(edge => {
            if (edge.id === edgeId) {
                const nextType = getNextEdgeType(String(edge.data?.edgeType));
                return {
                    ...edge,
                    data: { ...edge.data, edgeType: nextType, label: nextType },
                    type: nextType
                    // Let CustomEdge component handle style/marker updates based on data.edgeType
                };
            }
            return edge;
        })
    );
};

// --- Floating Edge Utilities ---

// Use InternalNode type which includes internals.positionAbsolute and internals.handleBounds
function getNodeCenter(node: InternalNode): XYPosition {
    // Use internals.positionAbsolute for accurate center calculation
    const xPos = node.internals.positionAbsolute?.x ?? node.position.x ?? 0;
    const yPos = node.internals.positionAbsolute?.y ?? node.position.y ?? 0;
    return {
        x: xPos + (node.width ?? 0) / 2,
        y: yPos + (node.height ?? 0) / 2
    };
}

// Use InternalNode type
function getHandleCoordsByPosition(node: InternalNode, handlePosition: Position): [number, number] {
    // Get node dimensions and position
    const nodeX = node.internals.positionAbsolute?.x ?? node.position.x ?? 0;
    const nodeY = node.internals.positionAbsolute?.y ?? node.position.y ?? 0;
    const nodeWidth = node.width ?? 0;
    const nodeHeight = node.height ?? 0;

    // First try to find an actual handle at the specified position
    const handles = node.internals.handleBounds;

    // Check both source and target handles based on position
    let handle;

    // Look for handles with the specific position in both source and target types
    // Try to find source handles first
    if (handlePosition === Position.Left) {
        handle = handles?.source?.find(h => h.id === 'left-source');
        if (!handle) handle = handles?.target?.find(h => h.id === 'left-target');
    } else if (handlePosition === Position.Right) {
        handle = handles?.source?.find(h => h.id === 'right-source');
        if (!handle) handle = handles?.target?.find(h => h.id === 'right-target');
    } else if (handlePosition === Position.Top) {
        handle = handles?.source?.find(h => h.id === 'top-source');
        if (!handle) handle = handles?.target?.find(h => h.id === 'top-target');
    } else if (handlePosition === Position.Bottom) {
        handle = handles?.source?.find(h => h.id === 'bottom-source');
        if (!handle) handle = handles?.target?.find(h => h.id === 'bottom-target');
    }

    // If we still didn't find a handle, try to find any handle with the matching position
    if (!handle) {
        // Try source handles first
        handle = handles?.source?.find(h => h.position === handlePosition);

        // If not found, try target handles
        if (!handle) {
            handle = handles?.target?.find(h => h.position === handlePosition);
        }
    }

    // If we found a handle, use its position
    if (handle) {
        let offsetX = (handle.width ?? 0) / 2;
        let offsetY = (handle.height ?? 0) / 2;

        switch (handlePosition) {
            case Position.Left:
                offsetX = 0;
                break;
            case Position.Right:
                offsetX = handle.width ?? 0;
                break;
            case Position.Top:
                offsetY = 0;
                break;
            case Position.Bottom:
                offsetY = handle.height ?? 0;
                break;
        }

        // Calculate absolute handle coordinates
        const x = nodeX + (handle.x ?? 0) + offsetX;
        const y = nodeY + (handle.y ?? 0) + offsetY;
        return [x, y];
    }

    // If no handle found, calculate position on the node edge based on the position
    // This ensures edges always connect to the node boundary even if no handle exists
    switch (handlePosition) {
        case Position.Left:
            return [nodeX, nodeY + nodeHeight / 2];
        case Position.Right:
            return [nodeX + nodeWidth, nodeY + nodeHeight / 2];
        case Position.Top:
            return [nodeX + nodeWidth / 2, nodeY];
        case Position.Bottom:
            return [nodeX + nodeWidth / 2, nodeY + nodeHeight];
        default:
            // Fallback to node center if position is invalid
            return [nodeX + nodeWidth / 2, nodeY + nodeHeight / 2];
    }
}

// Use InternalNode type
function getClosestEdgePosition(nodeA: InternalNode, nodeB: InternalNode): Position {
    const centerA = getNodeCenter(nodeA);
    const centerB = getNodeCenter(nodeB);

    // Calculate the angle between the centers of the two nodes
    const dx = centerB.x - centerA.x;
    const dy = centerB.y - centerA.y;
    const angle = Math.atan2(dy, dx);

    // Convert angle to degrees for easier comparison
    const angleDeg = angle * (180 / Math.PI);

    // Normalize angle to be between -180 and 180 degrees
    const normalizedAngle =
        angleDeg < -180 ? angleDeg + 360 : angleDeg > 180 ? angleDeg - 360 : angleDeg;

    // Determine the closest edge based on the angle
    // We divide the node into 4 quadrants and choose the edge accordingly

    // Simple quadrant-based approach for more balanced handle selection
    if (normalizedAngle >= -45 && normalizedAngle < 45) {
        return Position.Right; // Right edge
    } else if (normalizedAngle >= 45 && normalizedAngle < 135) {
        return Position.Bottom; // Bottom edge
    } else if (normalizedAngle >= 135 || normalizedAngle < -135) {
        return Position.Left; // Left edge
    } else {
        return Position.Top; // Top edge
    }
}

// Return type includes coordinates and positions
export function getSimpleFloatingEdgeParams(
    sourceNode: InternalNode,
    targetNode: InternalNode
): {
    sx: number;
    sy: number;
    tx: number;
    ty: number;
    sourcePos: Position;
    targetPos: Position;
} {
    const sourcePos = getClosestEdgePosition(sourceNode, targetNode);
    const targetPos = getClosestEdgePosition(targetNode, sourceNode);

    const [sx, sy] = getHandleCoordsByPosition(sourceNode, sourcePos);
    const [tx, ty] = getHandleCoordsByPosition(targetNode, targetPos);

    return {
        sx,
        sy,
        tx,
        ty,
        sourcePos,
        targetPos
    };
}
