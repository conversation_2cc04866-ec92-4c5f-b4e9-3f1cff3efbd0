import { create } from 'zustand';
import {
    Node,
    Edge,
    OnNodesChange,
    OnEdgesChange,
    NodeTypes,
    EdgeTypes,
    applyNodeChanges,
    applyEdgeChanges
} from '@xyflow/react';
import { onPaneDoubleClickAddNode } from './utils/onPaneDoubleClickAddNode';
import pkg from 'lodash';
const { debounce } = pkg;

// Define the core map store state interface
interface CoreMapStoreState {
    // ReactFlow state
    nodes: Node[];
    edges: Edge[];
    nodeTypes: NodeTypes;
    edgeTypes: EdgeTypes;
    isLoading: boolean;
    error: Error | null;

    // ReactFlow event handlers
    onNodesChange: OnNodesChange;
    onEdgesChange: OnEdgesChange;
    onDoubleClick: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;

    // Map data methods
    setNodes: (nodes: Node[] | ((prevNodes: Node[]) => Node[])) => void;
    setEdges: (edges: Edge[] | ((prevEdges: Edge[]) => Edge[])) => void;
    setNodeTypes: (nodeTypes: NodeTypes) => void;
    setEdgeTypes: (edgeTypes: EdgeTypes) => void;
    debouncedUpdateMap: () => void;
    addChangedNodeId: (nodeId: string | string[]) => void;
}

export const useMapStore = create<CoreMapStoreState>((set, get) => ({
    // ReactFlow state
    nodes: [],
    edges: [],
    nodeTypes: {},
    edgeTypes: {},
    isLoading: false,
    error: null,

    // ReactFlow event handlers
    onNodesChange: changes => {
        set(state => ({
            nodes: applyNodeChanges(changes, state.nodes)
        }));
        get().debouncedUpdateMap();
    },

    onEdgesChange: changes => {
        set(state => ({
            edges: applyEdgeChanges(changes, state.edges)
        }));
        get().debouncedUpdateMap();
    },

    // onConnect will be handled by EdgeStore

    onDoubleClick: event => {
        console.log('[MapStore:onDoubleClick] Double click event:', event);
        console.log('[MapStore:onDoubleClick] Event target:', event.target);
        console.log(
            '[MapStore:onDoubleClick] Event target classes:',
            (event.target as Element)?.classList
        );

        // Use the utility function to add a new node
        // We'll create a simple mock ReactFlow instance for coordinate conversion
        const reactFlowWrapper = document.querySelector('.react-flow');
        if (!reactFlowWrapper) {
            console.error('[MapStore:onDoubleClick] ReactFlow wrapper not found');
            return;
        }

        const reactFlowBounds = reactFlowWrapper.getBoundingClientRect();
        const mockReactFlowInstance = {
            screenToFlowPosition: (position: { x: number; y: number }) => {
                const flowPosition = {
                    x: position.x - reactFlowBounds.left,
                    y: position.y - reactFlowBounds.top
                };
                console.log(
                    '[MapStore:onDoubleClick] Converting screen position:',
                    position,
                    'to flow position:',
                    flowPosition
                );
                return flowPosition;
            }
        };

        // Use the utility function to add a new node
        onPaneDoubleClickAddNode(event, mockReactFlowInstance as any, get().setNodes);

        // Update the map in the database
        get().debouncedUpdateMap();
    },

    // Context menu and node event handlers will be handled by their respective stores

    // Map data methods
    setNodes: nodes => {
        if (typeof nodes === 'function') {
            set(state => ({ nodes: nodes(state.nodes) }));
        } else {
            set({ nodes });
        }
    },

    setEdges: edges => {
        if (typeof edges === 'function') {
            set(state => ({ edges: edges(state.edges) }));
        } else {
            set({ edges });
        }
    },

    setNodeTypes: nodeTypes => {
        console.log('[MapStore:setNodeTypes] Setting node types:', Object.keys(nodeTypes));
        set({ nodeTypes });
    },

    setEdgeTypes: edgeTypes => {
        console.log('[MapStore:setEdgeTypes] Setting edge types:', Object.keys(edgeTypes));
        set({ edgeTypes });
    },

    debouncedUpdateMap: debounce(() => {
        // This will be implemented to save map data to the database
        console.log('[MapStore:debouncedUpdateMap] Updating map data in database');
    }, 500), // 500ms debounce time

    addChangedNodeId: nodeId => {
        // This will be implemented to track changed nodes
        console.log('[MapStore:addChangedNodeId] Adding changed node ID:', nodeId);
    }
}));
