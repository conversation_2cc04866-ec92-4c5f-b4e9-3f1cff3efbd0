import { Node, ReactFlowInstance } from '@xyflow/react';

export const onPaneDoubleClickAddNode = (
    event: React.MouseEvent<HTMLDivElement>,
    reactFlowInstance: ReactFlowInstance,
    setNodes: (updater: React.SetStateAction<Node[]>) => void
) => {
    console.log('[onPaneDoubleClickAddNode] Function called with event:', event);
    console.log('[onPaneDoubleClickAddNode] Event target:', event.target);
    console.log(
        '[onPaneDoubleClickAddNode] Event target classes:',
        (event.target as Element)?.classList
    );

    // Check if the click was on the pane
    if (
        !(event.target instanceof Element) ||
        !event.target.classList.contains('react-flow__pane')
    ) {
        console.log('[onPaneDoubleClickAddNode] Click was not on pane, ignoring');
        return;
    }

    console.log('[onPaneDoubleClickAddNode] Click was on pane, proceeding to create node');

    // Get the position where the user double-clicked in the viewport
    const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY
    });

    console.log('[onPaneDoubleClickAddNode] Calculated position:', position);

    // Create a unique ID for the new node
    const newNodeId = crypto.randomUUID();

    // Create the new node with default properties
    const newNode: Node = {
        id: newNodeId,
        type: 'concept',
        position,
        data: {
            conceptTitle: 'New Concept', // Give it a default title
            progress: 0,
            cardCount: 0,
            learningStatus: 'notStarted',
            tags: [],
            sections: {}
        }
    };

    console.log('[onPaneDoubleClickAddNode] Created new node:', newNode);
    console.log('[onPaneDoubleClickAddNode] Node type:', newNode.type);

    // Add the new node to the flow
    setNodes(nds => {
        console.log('[onPaneDoubleClickAddNode] Current nodes before adding:', nds.length);
        const newNodes = [...nds, newNode];
        console.log('[onPaneDoubleClickAddNode] New nodes after adding:', newNodes.length);
        return newNodes;
    });

    console.log('[onPaneDoubleClickAddNode] Node addition completed');
};
