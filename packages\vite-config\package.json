{"name": "@repo/vite-config", "version": "0.0.0", "private": true, "type": "module", "exports": {".": "./index.js", "./storybook": "./storybook.js"}, "devDependencies": {"@react-router/dev": "^7.0.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@svgr/rollup": "^8.1.0", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "tailwindcss": "^3.4.15", "typescript": "^5.7.2", "unplugin-auto-import": "^0.18.6", "vite": "^5.4.11", "vite-plugin-checker": "^0.8.0", "vite-tsconfig-paths": "^5.1.2", "vitest": "^2.1.8"}, "peerDependencies": {"terser": ">=5.36.0"}}