import { Outlet } from 'react-router';
import { SidebarModule } from '@repo/ui';
import { getSessionAndSubscribe } from '@repo/db';
import { Review } from '@repo/review';
import { useGlobalStore } from '@repo/utils';
import { useEffect } from 'react';

// --- App Content Component ---
export default function AppContent() {
    // Set up authentication subscription only once when component mounts
    useEffect(() => {
        console.log('Setting up Triplit session subscription');
        const unsubscribe = getSessionAndSubscribe();

        // Clean up subscription when component unmounts
        return () => {
            console.log('Cleaning up Triplit session subscription');
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, []);

    // Get isReviewActive from global store
    const isReviewActive = useGlobalStore(state => state.isReviewActive);
    const activeDeckId = useGlobalStore(state => state.activeDeckId);
    const setIsReviewActive = useGlobalStore(state => state.setIsReviewActive);

    // Handle closing the review
    const handleCloseReview = () => {
        setIsReviewActive(false);
    };

    return (
        <div className='flex min-h-screen flex-col'>
            <div className='flex flex-1 overflow-hidden'>
                <SidebarModule />
                <main className='flex-1 overflow-auto'>
                    <Outlet />
                </main>
            </div>

            {/* Render Review component when isReviewActive is true */}
            {isReviewActive && (
                <Review deckId={activeDeckId || undefined} onClose={handleCloseReview} />
            )}
        </div>
    );
}
