import { ReactFlow, Background, BackgroundVariant, Panel, ReactFlowProvider } from '@xyflow/react';
import '@xyflow/react/dist/base.css';
import { FloatingMenu } from '../FloatingMenu/FloatingMenu';
import { MapZoom } from '../Zoom/MapZoom';
import { useMapStore } from './MapStore';
import { useEdgeStore } from '../edges/EdgeStore';
import { useContextMenuStore } from '../ContextMenu/ContextMenuStore';
import { useNodeStore } from '../nodes/NodeStore';
import { ConceptNode } from '../nodes/ConceptNode/ConceptNode';
import { edgeTypes } from '../nodes/ConceptNode/edges/CustomEdges';
import { useEffect } from 'react';

export function Map() {
    return (
        <ReactFlowProvider>
            <MapContent />
        </ReactFlowProvider>
    );
}

function MapContent() {
    // Get core ReactFlow state from the map store
    const {
        nodes,
        edges,
        nodeTypes,
        edgeTypes,
        isLoading,
        error,
        onNodesChange,
        onEdgesChange,
        onDoubleClick,
        setNodeTypes,
        setEdgeTypes
    } = useMapStore();

    // Debug: Log current nodes and their types
    useEffect(() => {
        console.log('[Map] Current nodes:', nodes);
        console.log('[Map] Current node types registered:', Object.keys(nodeTypes));
        nodes.forEach(node => {
            console.log(`[Map] Node ${node.id}: type="${node.type}", data:`, node.data);
        });
    }, [nodes, nodeTypes]);

    // Get edge handlers from edge store
    const { onConnect } = useEdgeStore();

    // Get context menu handlers from context menu store
    const { onContextMenu, onNodeContextMenu } = useContextMenuStore();

    // Get node handlers from node store
    const { onNodeDrag, onNodeDragStop } = useNodeStore();

    // Initialize node and edge types when component mounts
    useEffect(() => {
        console.log('[Map] Initializing node and edge types');

        // Register node types
        const nodeTypesConfig = {
            concept: ConceptNode
        };
        console.log('[Map] Registering node types:', Object.keys(nodeTypesConfig));
        console.log('[Map] ConceptNode component:', ConceptNode);
        setNodeTypes(nodeTypesConfig);

        // Register edge types
        console.log('[Map] Registering edge types:', Object.keys(edgeTypes));
        setEdgeTypes(edgeTypes);
    }, [setNodeTypes, setEdgeTypes]);

    // Handle adding concepts from the floating menu
    const handleAddConcepts = () => {
        console.log('[Map:handleAddConcepts] Adding concepts');
        // Implementation will be added later
    };

    if (isLoading) {
        return <div className='flex h-full w-full items-center justify-center'>Loading map...</div>;
    }

    if (error) {
        return (
            <div className='flex h-full w-full items-center justify-center'>
                Error loading map: {error.message}
            </div>
        );
    }

    return (
        <div className='map-container relative h-full w-full'>
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onDoubleClick={onDoubleClick}
                onContextMenu={onContextMenu}
                onNodeContextMenu={onNodeContextMenu}
                onNodeDrag={onNodeDrag}
                onNodeDragStop={onNodeDragStop}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                panOnScroll={false}
                selectionOnDrag={true}
                zoomOnDoubleClick={false}
                zoomOnScroll={false}
                zoomOnPinch={false}
                preventScrolling={true}
                maxZoom={3}
                minZoom={0.1}
                onWheelCapture={e => e.preventDefault()}
                className='h-[100vh] w-full'
                selectionKeyCode={['Shift']}
                multiSelectionKeyCode={['Control', 'Meta']}
                deleteKeyCode={'Delete'}
                selectNodesOnDrag={false}
                elementsSelectable={true}
                nodesDraggable={true}
                panOnDrag={[2]}
                defaultEdgeOptions={{ type: 'default' }}
                elevateNodesOnSelect={true}
                elevateEdgesOnSelect={true}
            >
                <Background variant={BackgroundVariant.Dots} />
                <Panel position='bottom-center'>
                    <FloatingMenu onAddConcepts={handleAddConcepts} />
                </Panel>
                <Panel position='bottom-right'>
                    <MapZoom />
                </Panel>
            </ReactFlow>
        </div>
    );
}
