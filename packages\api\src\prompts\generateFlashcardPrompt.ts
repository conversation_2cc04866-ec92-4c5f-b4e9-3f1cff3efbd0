import { NodeSection } from '../openai';
import {
    formatSectionLabels,
    getLearningOutcome,
    getLanguageInstruction,
    getExpertIntro
} from './promptWrapper';

// Helper function to generate a prompt for creating flashcards
export const generateFlashcardsPrompt = (
    sourceConceptTitle: string,
    sourceSections: Record<string, NodeSection>,
    deckTitle?: string,
    learningOutcome?: string,
    language: string = 'en',
    temperature?: number,
    maxTokens?: number
): string => {
    // Log the temperature and maxTokens for debugging
    logSettings('generateFlashcardsPrompt', temperature, maxTokens);

    // Format existing sections
    const existingSectionLabels = formatSectionLabels(sourceSections);

    // Build the prompt
    return `${getExpertIntro()} Your task is to create a set of diverse flashcards for the concept
"${sourceConceptTitle}"${deckTitle ? ` in the context of ${deckTitle}` : ''}.

IMPORTANT GUIDELINES:
1. Create a variety of flashcard types to test different aspects of knowledge:
   - Explain: Ask the user to explain the concept in their own words
   - Explain Like I'm Five: Ask the user to explain the concept in simple terms
   - Q/A: Direct question and answer pairs
   - Match: Matching terms with definitions
   - Choice: Multiple choice questions
   - Sort: Sorting steps or elements in the correct order
   - Connect: Connecting related elements
   - Fill-in-the-blank: Sentences with key terms removed

2. For each flashcard, include the correct answer in double curly braces {{like this}}.
3. Make the flashcards challenging but fair, focusing on the most important aspects of the concept.
4. Create flashcards that test understanding, not just memorization.

CONTEXT:
This concept has the following sections: ${existingSectionLabels || 'None yet'}

${getLearningOutcome(learningOutcome)}

Format your response as a JSON array with objects containing "card_type" and "card_text" properties.
The card_type must be one of: "explain", "explain_like_im_five", "match", "choice", "sort", "truefalse", "cloze", "exercise".

Example format:
[
  {
    "card_type": "explain",
    "card_text": "Explain the concept of [Concept] in your own words. {{A comprehensive explanation would include...}}"
  },
  {
    "card_type": "cloze",
    "card_text": "The Eiffel Tower is located in {{Paris}}."
  },
  {
    "card_type": "sort",
    "card_text": "What are the 3 steps to evalualte a portfolio? {{1.Analyse }} {{2.Optimize}} {{3.Document}}"
  },
  {
    "card_type": "truefalse",
    "card_text": "Is the Eiffel Tower located in Paris? {{True}}"
  }
  ...
]

Create at least 5 different types of flashcards for this concept. STRICTLY use plain and easy to understand language and only information from the node itself!

${getLanguageInstruction(language)}`;
};
