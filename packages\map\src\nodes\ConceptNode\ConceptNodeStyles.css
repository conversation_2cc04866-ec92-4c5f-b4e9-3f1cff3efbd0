/* Override ReactFlow node styling */
.react-flow__node {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  font-size: inherit !important;
  font-family: inherit !important;
  color: inherit !important;
}

/* Ensure our custom node styling takes precedence */
.react-flow__node-default {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  width: auto !important;
  height: auto !important;
}

/* Remove any selection styling from ReactFlow */
.react-flow__node.selected,
.react-flow__node.selected:hover {
  box-shadow: none !important;
  border-color: #e5e7eb !important;
}
