import { Map } from '@repo/map';
import { useParams } from 'react-router';
import { useEffect } from 'react';
import { useGlobalStore } from '@repo/utils';
import { triplit } from '@repo/db';

export default function MapId() {
    const params = useParams();
    const mapId = params.map_id;

    const setActiveDeckId = useGlobalStore(state => state.setActiveDeckId);
    const setActiveDeckName = useGlobalStore(state => state.setActiveDeckName);
    const setActiveDeckLearningOutcome = useGlobalStore(
        state => state.setActiveDeckLearningOutcome
    );
    const setActiveDeckLanguage = useGlobalStore(state => state.setActiveDeckLanguage);

    // Set the active deck when component mounts or mapId changes
    useEffect(() => {
        if (mapId) {
            console.log('[MapId] Setting active deck ID:', mapId);
            setActiveDeckId(mapId);

            // Fetch and set deck details
            const fetchDeckDetails = async () => {
                try {
                    const deck = await triplit.fetchById('decks', mapId);
                    if (deck) {
                        console.log('[MapId] Setting deck details:', deck);
                        setActiveDeckName(deck.name || null);
                        setActiveDeckLearningOutcome(deck.learning_outcome || null);
                        setActiveDeckLanguage(deck.language || 'en');
                    }
                } catch (error) {
                    console.error('[MapId] Error fetching deck details:', error);
                }
            };

            fetchDeckDetails();
        }

        // Cleanup: clear active deck when component unmounts
        return () => {
            console.log('[MapId] Clearing active deck ID');
            setActiveDeckId(null);
            setActiveDeckName(null);
            setActiveDeckLearningOutcome(null);
            setActiveDeckLanguage(null);
        };
    }, [
        mapId,
        setActiveDeckId,
        setActiveDeckName,
        setActiveDeckLearningOutcome,
        setActiveDeckLanguage
    ]);

    return (
        <div className='relative h-[100vh]'>
            <Map />
        </div>
    );
}
