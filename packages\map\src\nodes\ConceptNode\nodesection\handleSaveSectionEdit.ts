import { SectionConfig } from '../../../Map/interfaces/SectionConfig';

export const saveSectionEdit = (
    editingSectionId: string | null,
    editingValue: string,
    sections: Record<string, SectionConfig>,
    updateNodeData: (data: Record<string, any>) => void,
    setEditingSectionId: (id: string | null) => void,
    setEditingValue: (value: string) => void
): void => {
    if (editingSectionId) {
        const updatedSections = {
            ...sections,
            [editingSectionId]: {
                ...sections[editingSectionId],
                value: editingValue.trim() // Trim whitespace on save
            }
        };

        updateNodeData({ sections: updatedSections });
        setEditingSectionId(null);
        setEditingValue('');
    }
};
