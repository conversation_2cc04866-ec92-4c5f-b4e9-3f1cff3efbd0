import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { supabase } from '@repo/auth';
import {
    <PERSON><PERSON>,
    Card,
    CardHeader,
    CardContent,
    CardTitle,
    CardDescription,
    Form,
    FormField,
    FormItem,
    FormControl,
    FormLabel,
    FormDescription,
    Alert,
    AlertTitle,
    AlertDescription,
    Switch
} from '@repo/ui';
import { TbBell } from 'react-icons/tb';

// Schema for notification settings
const notificationSchema = z.object({
    emailNotifications: z.boolean().default(true),
    studyReminders: z.boolean().default(true),
    marketingEmails: z.boolean().default(false),
    newFeatures: z.boolean().default(true)
});

type NotificationFormValues = z.infer<typeof notificationSchema>;

interface NotificationFormProps {
    user: any;
}

export default function NotificationForm({ user }: NotificationFormProps) {
    const [notificationSuccess, setNotificationSuccess] = useState<string | null>(null);
    const [notificationError, setNotificationError] = useState<string | null>(null);
    const [isNotificationLoading, setIsNotificationLoading] = useState(false);

    // Notification form
    const notificationForm = useForm<NotificationFormValues>({
        resolver: zodResolver(notificationSchema),
        defaultValues: {
            emailNotifications: user?.user_metadata?.emailNotifications !== false,
            studyReminders: user?.user_metadata?.studyReminders !== false,
            marketingEmails: user?.user_metadata?.marketingEmails === true,
            newFeatures: user?.user_metadata?.newFeatures !== false
        }
    });

    // Handle notification settings update
    async function onNotificationSubmit(values: NotificationFormValues) {
        setIsNotificationLoading(true);
        setNotificationSuccess(null);
        setNotificationError(null);

        try {
            const { error } = await supabase.auth.updateUser({
                data: {
                    emailNotifications: values.emailNotifications,
                    studyReminders: values.studyReminders,
                    marketingEmails: values.marketingEmails,
                    newFeatures: values.newFeatures
                }
            });

            if (error) {
                throw error;
            }

            setNotificationSuccess('Notification preferences updated successfully');
        } catch (error: any) {
            setNotificationError(error.message || 'Failed to update notification preferences');
        } finally {
            setIsNotificationLoading(false);
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                    <TbBell className='h-5 w-5 text-primary' />
                    Notification Preferences
                </CardTitle>
                <CardDescription>Manage how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent>
                <Form {...notificationForm}>
                    <form
                        onSubmit={notificationForm.handleSubmit(onNotificationSubmit)}
                        className='space-y-4'
                    >
                        <FormField
                            control={notificationForm.control}
                            name='emailNotifications'
                            render={({ field }) => (
                                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                                    <div className='space-y-0.5'>
                                        <FormLabel className='text-base'>
                                            Email Notifications
                                        </FormLabel>
                                        <FormDescription>
                                            Receive email notifications about your account
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={notificationForm.control}
                            name='studyReminders'
                            render={({ field }) => (
                                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                                    <div className='space-y-0.5'>
                                        <FormLabel className='text-base'>Study Reminders</FormLabel>
                                        <FormDescription>
                                            Get reminders when it's time to study
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={notificationForm.control}
                            name='newFeatures'
                            render={({ field }) => (
                                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                                    <div className='space-y-0.5'>
                                        <FormLabel className='text-base'>New Features</FormLabel>
                                        <FormDescription>
                                            Get notified about new features and updates
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={notificationForm.control}
                            name='marketingEmails'
                            render={({ field }) => (
                                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                                    <div className='space-y-0.5'>
                                        <FormLabel className='text-base'>
                                            Marketing Emails
                                        </FormLabel>
                                        <FormDescription>
                                            Receive marketing emails and promotions
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        {notificationSuccess && (
                            <Alert className='border-green-200 bg-green-50'>
                                <AlertTitle>Success</AlertTitle>
                                <AlertDescription>{notificationSuccess}</AlertDescription>
                            </Alert>
                        )}

                        {notificationError && (
                            <Alert className='border-red-200 bg-red-50'>
                                <AlertTitle>Error</AlertTitle>
                                <AlertDescription>{notificationError}</AlertDescription>
                            </Alert>
                        )}

                        <Button type='submit' disabled={isNotificationLoading}>
                            {isNotificationLoading ? 'Saving...' : 'Save Preferences'}
                        </Button>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
}
