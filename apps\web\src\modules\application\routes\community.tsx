import { useState, useEffect } from 'react';
import { DeckDocType } from '@repo/db';
import {
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
    Input,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Badge,
    Skeleton
} from '@repo/ui';
import { Search, Star, Users, TrendingUp, Clock, Filter, BookOpen } from 'lucide-react';

// Define the extended deck type with additional community properties
interface CommunityDeck extends DeckDocType {
    user_email?: string;
    rating?: number;
    ratings_count?: number;
    cards_count?: number;
}

// Rating component
function DeckRating({ rating, count }: { rating: number; count: number }) {
    return (
        <div className='flex items-center gap-1'>
            <Star
                className={`h-4 w-4 ${rating > 0 ? 'fill-yellow-500 text-yellow-500' : 'text-gray-300'}`}
            />
            <span className='text-sm font-medium'>{rating.toFixed(1)}</span>
            <span className='text-xs text-gray-500'>({count})</span>
        </div>
    );
}

// Deck Card component
function DeckCard({
    deck,
    onView
}: {
    deck: CommunityDeck;
    onView: (deck: CommunityDeck) => void;
}) {
    return (
        <Card className='overflow-hidden transition-all duration-200 hover:shadow-md'>
            <CardHeader className='pb-2'>
                <div className='flex justify-between'>
                    <CardTitle className='truncate text-lg font-semibold'>{deck.name}</CardTitle>
                    <DeckRating rating={deck.rating || 0} count={deck.ratings_count || 0} />
                </div>
                <CardDescription className='flex items-center gap-1 text-xs'>
                    <Users className='h-3 w-3' />
                    <span>{deck.user_email || 'Anonymous'}</span>
                </CardDescription>
            </CardHeader>
            <CardContent className='pb-2'>
                <div className='flex flex-wrap gap-2'>
                    <Badge variant='outline' className='bg-gray-50 text-xs'>
                        {deck.type}
                    </Badge>
                    <Badge variant='outline' className='bg-gray-50 text-xs'>
                        <BookOpen className='mr-1 h-3 w-3' />
                        {deck.cards_count || 0} cards
                    </Badge>
                </div>
            </CardContent>
            <CardFooter className='flex justify-between pt-2'>
                <div className='text-xs text-gray-500'>
                    <Clock className='mr-1 inline-block h-3 w-3' />
                    {new Date(deck.created_at).toLocaleDateString()}
                </div>
                <Button size='sm' onClick={() => onView(deck)}>
                    View
                </Button>
            </CardFooter>
        </Card>
    );
}

// Skeleton loader for deck cards
function DeckCardSkeleton() {
    return (
        <Card className='overflow-hidden'>
            <CardHeader className='pb-2'>
                <div className='flex justify-between'>
                    <Skeleton className='h-6 w-3/4' />
                    <Skeleton className='h-4 w-16' />
                </div>
                <Skeleton className='h-4 w-1/2' />
            </CardHeader>
            <CardContent className='pb-2'>
                <div className='flex gap-2'>
                    <Skeleton className='h-5 w-16' />
                    <Skeleton className='h-5 w-24' />
                </div>
            </CardContent>
            <CardFooter className='flex justify-between pt-2'>
                <Skeleton className='h-4 w-24' />
                <Skeleton className='h-8 w-16' />
            </CardFooter>
        </Card>
    );
}

// Community Stats component
function CommunityStats({ stats }: { stats: { decks: number; users: number; cards: number } }) {
    return (
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-3'>
            <Card>
                <CardHeader className='pb-2'>
                    <CardTitle className='text-sm font-medium text-gray-500'>Total Decks</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className='flex items-center'>
                        <BookOpen className='mr-2 h-5 w-5 text-indigo-500' />
                        <span className='text-2xl font-bold'>{stats.decks}</span>
                    </div>
                </CardContent>
            </Card>
            <Card>
                <CardHeader className='pb-2'>
                    <CardTitle className='text-sm font-medium text-gray-500'>
                        Community Users
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className='flex items-center'>
                        <Users className='mr-2 h-5 w-5 text-indigo-500' />
                        <span className='text-2xl font-bold'>{stats.users}</span>
                    </div>
                </CardContent>
            </Card>
            <Card>
                <CardHeader className='pb-2'>
                    <CardTitle className='text-sm font-medium text-gray-500'>Total Cards</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className='flex items-center'>
                        <TrendingUp className='mr-2 h-5 w-5 text-indigo-500' />
                        <span className='text-2xl font-bold'>{stats.cards}</span>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

// Deck Detail Modal component
function DeckDetailModal({
    deck,
    onClose,
    onRate,
    userRating,
    isLoading
}: {
    deck: CommunityDeck | null;
    onClose: () => void;
    onRate: (rating: number) => void;
    userRating: number | null;
    isLoading: boolean;
}) {
    if (!deck) return null;

    return (
        <div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50'>
            <div className='w-full max-w-2xl rounded-lg bg-white p-6 shadow-lg'>
                <div className='mb-4 flex items-center justify-between'>
                    <h2 className='text-xl font-bold'>{deck.name}</h2>
                    <Button variant='ghost' size='sm' onClick={onClose}>
                        ✕
                    </Button>
                </div>

                <div className='mb-4 grid grid-cols-2 gap-4'>
                    <div>
                        <p className='text-sm text-gray-500'>Created by</p>
                        <p className='font-medium'>{deck.user_email || 'Anonymous'}</p>
                    </div>
                    <div>
                        <p className='text-sm text-gray-500'>Type</p>
                        <p className='font-medium'>{deck.type}</p>
                    </div>
                    <div>
                        <p className='text-sm text-gray-500'>Cards</p>
                        <p className='font-medium'>{deck.cards_count || 0}</p>
                    </div>
                    <div>
                        <p className='text-sm text-gray-500'>Created</p>
                        <p className='font-medium'>
                            {new Date(deck.created_at).toLocaleDateString()}
                        </p>
                    </div>
                </div>

                <div className='mb-6'>
                    <p className='mb-2 text-sm font-medium'>Rate this deck</p>
                    <div className='flex items-center gap-2'>
                        {[1, 2, 3, 4, 5].map(rating => (
                            <button
                                key={rating}
                                disabled={isLoading}
                                onClick={() => onRate(rating)}
                                className={`rounded-full p-1 transition-colors ${
                                    userRating && rating <= userRating
                                        ? 'text-yellow-500 hover:text-yellow-600'
                                        : 'text-gray-300 hover:text-yellow-400'
                                }`}
                            >
                                <Star
                                    className={`h-6 w-6 ${
                                        userRating && rating <= userRating ? 'fill-yellow-500' : ''
                                    }`}
                                />
                            </button>
                        ))}
                        <span className='ml-2 text-sm text-gray-500'>
                            {userRating ? `Your rating: ${userRating}` : 'Not rated yet'}
                        </span>
                    </div>
                </div>

                <div className='flex justify-end gap-2'>
                    <Button variant='outline' onClick={onClose}>
                        Close
                    </Button>
                    <Button>Add to My Decks</Button>
                </div>
            </div>
        </div>
    );
}

// Main Community component
export default function Community() {
    const { user } = useAuth();
    const [communityDecks, setCommunityDecks] = useState<CommunityDeck[]>([]);
    const [userDecks, setUserDecks] = useState<CommunityDeck[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [sortBy, setSortBy] = useState('rating');
    const [selectedDeck, setSelectedDeck] = useState<CommunityDeck | null>(null);
    const [userRating, setUserRating] = useState<number | null>(null);
    const [isRatingLoading, setIsRatingLoading] = useState(false);
    const [communityStats, setCommunityStats] = useState({
        decks: 0,
        users: 0,
        cards: 0
    });

    // Fetch community decks
    useEffect(() => {
        async function fetchCommunityDecks() {
            if (!user) return;

            setIsLoading(true);
            try {
                // Fetch public decks from other users
                const { data: publicDecks, error } = await supabase
                    .from('decks')
                    .select(
                        `
            *,
            user_email:user_id(email),
            ratings:deck_ratings(rating),
            cards_count:cards(count)
          `
                    )
                    .eq('is_public', true)
                    .neq('user_id', user.id);

                if (error) throw error;

                // Process the decks to calculate average rating
                const processedDecks = publicDecks.map((deck: any) => {
                    const ratings = deck.ratings || [];
                    const ratingSum = ratings.reduce((sum: number, r: any) => sum + r.rating, 0);
                    const averageRating = ratings.length > 0 ? ratingSum / ratings.length : 0;

                    return {
                        ...deck,
                        user_email: deck.user_email?.[0]?.email || 'Anonymous',
                        rating: averageRating,
                        ratings_count: ratings.length,
                        cards_count: deck.cards_count?.[0]?.count || 0
                    };
                });

                setCommunityDecks(processedDecks);

                // Fetch user's public decks
                const { data: myPublicDecks, error: myDecksError } = await supabase
                    .from('decks')
                    .select(
                        `
            *,
            ratings:deck_ratings(rating),
            cards_count:cards(count)
          `
                    )
                    .eq('user_id', user.id)
                    .eq('is_public', true);

                if (myDecksError) throw myDecksError;

                // Process user's decks
                const processedUserDecks = myPublicDecks.map((deck: any) => {
                    const ratings = deck.ratings || [];
                    const ratingSum = ratings.reduce((sum: number, r: any) => sum + r.rating, 0);
                    const averageRating = ratings.length > 0 ? ratingSum / ratings.length : 0;

                    return {
                        ...deck,
                        user_email: user.email,
                        rating: averageRating,
                        ratings_count: ratings.length,
                        cards_count: deck.cards_count?.[0]?.count || 0
                    };
                });

                setUserDecks(processedUserDecks);

                // Fetch community stats
                const { data: statsData, error: statsError } =
                    await supabase.rpc('get_community_stats');

                if (!statsError && statsData) {
                    setCommunityStats({
                        decks: statsData.total_public_decks || 0,
                        users: statsData.unique_users || 0,
                        cards: statsData.total_cards || 0
                    });
                } else {
                    // Fallback if RPC not available
                    setCommunityStats({
                        decks: processedDecks.length + processedUserDecks.length,
                        users: new Set([...processedDecks.map((d: any) => d.user_id)]).size + 1, // +1 for current user
                        cards:
                            processedDecks.reduce(
                                (sum: number, d: any) => sum + (d.cards_count || 0),
                                0
                            ) +
                            processedUserDecks.reduce(
                                (sum: number, d: any) => sum + (d.cards_count || 0),
                                0
                            )
                    });
                }
            } catch (error) {
                console.error('Error fetching community decks:', error);
            } finally {
                setIsLoading(false);
            }
        }

        fetchCommunityDecks();
    }, [user]);

    // Handle viewing a deck
    const handleViewDeck = async (deck: CommunityDeck) => {
        setSelectedDeck(deck);

        // Check if user has already rated this deck
        if (user) {
            try {
                const { data, error } = await supabase
                    .from('deck_ratings')
                    .select('rating')
                    .eq('user_id', user.id)
                    .eq('deck_id', deck.id)
                    .single();

                if (!error && data) {
                    setUserRating(data.rating);
                } else {
                    setUserRating(null);
                }
            } catch (error) {
                console.error('Error fetching user rating:', error);
                setUserRating(null);
            }
        }
    };

    // Handle rating a deck
    const handleRateDeck = async (rating: number) => {
        if (!user || !selectedDeck) return;

        setIsRatingLoading(true);
        try {
            // Check if rating already exists
            const { data: existingRating } = await supabase
                .from('deck_ratings')
                .select('id')
                .eq('user_id', user.id)
                .eq('deck_id', selectedDeck.id)
                .single();

            if (existingRating) {
                // Update existing rating
                await supabase
                    .from('deck_ratings')
                    .update({ rating, updated_at: new Date().toISOString() })
                    .eq('id', existingRating.id);
            } else {
                // Insert new rating
                await supabase.from('deck_ratings').insert({
                    id: crypto.randomUUID(),
                    user_id: user.id,
                    deck_id: selectedDeck.id,
                    rating,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                });
            }

            setUserRating(rating);

            // Refresh the decks to update ratings
            // This is a simplified approach - in a real app you might want to update just the affected deck
            const { data: updatedDecks, error } = await supabase
                .from('decks')
                .select(
                    `
          *,
          user_email:user_id(email),
          ratings:deck_ratings(rating),
          cards_count:cards(count)
        `
                )
                .eq('is_public', true);

            if (!error && updatedDecks) {
                // Process the decks to calculate average rating
                const processedDecks = updatedDecks.map((deck: any) => {
                    const ratings = deck.ratings || [];
                    const ratingSum = ratings.reduce((sum: number, r: any) => sum + r.rating, 0);
                    const averageRating = ratings.length > 0 ? ratingSum / ratings.length : 0;

                    return {
                        ...deck,
                        user_email: deck.user_email?.[0]?.email || 'Anonymous',
                        rating: averageRating,
                        ratings_count: ratings.length,
                        cards_count: deck.cards_count?.[0]?.count || 0
                    };
                });

                // Update community decks and user decks
                setCommunityDecks(processedDecks.filter((d: any) => d.user_id !== user.id));
                setUserDecks(processedDecks.filter((d: any) => d.user_id === user.id));

                // Update selected deck with new rating info
                const updatedSelectedDeck = processedDecks.find(
                    (d: any) => d.id === selectedDeck.id
                );
                if (updatedSelectedDeck) {
                    setSelectedDeck(updatedSelectedDeck);
                }
            }
        } catch (error) {
            console.error('Error rating deck:', error);
        } finally {
            setIsRatingLoading(false);
        }
    };

    // Filter and sort decks based on search query and sort option
    const filteredCommunityDecks = communityDecks
        .filter(deck => deck.name.toLowerCase().includes(searchQuery.toLowerCase()))
        .sort((a, b) => {
            if (sortBy === 'rating') return (b.rating || 0) - (a.rating || 0);
            if (sortBy === 'newest')
                return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            if (sortBy === 'popular') return (b.ratings_count || 0) - (a.ratings_count || 0);
            return 0;
        });

    const filteredUserDecks = userDecks
        .filter(deck => deck.name.toLowerCase().includes(searchQuery.toLowerCase()))
        .sort((a, b) => {
            if (sortBy === 'rating') return (b.rating || 0) - (a.rating || 0);
            if (sortBy === 'newest')
                return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            if (sortBy === 'popular') return (b.ratings_count || 0) - (a.ratings_count || 0);
            return 0;
        });

    return (
        <div className='container mx-auto py-6'>
            <div className='mb-8'>
                <h1 className='mb-2 text-3xl font-bold'>Community</h1>
                <p className='text-gray-600'>
                    Discover and share decks with the community. Rate decks and find the best
                    learning resources.
                </p>
            </div>

            <div className='mb-8'>
                <CommunityStats stats={communityStats} />
            </div>

            <div className='mb-6'>
                <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
                    <div className='relative flex-1'>
                        <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400' />
                        <Input
                            placeholder='Search decks...'
                            className='pl-9'
                            value={searchQuery}
                            onChange={e => setSearchQuery(e.target.value)}
                        />
                    </div>
                    <div className='flex items-center gap-2'>
                        <Filter className='h-4 w-4 text-gray-500' />
                        <Select value={sortBy} onValueChange={setSortBy}>
                            <SelectTrigger className='w-[180px]'>
                                <SelectValue placeholder='Sort by' />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value='rating'>Highest Rated</SelectItem>
                                <SelectItem value='newest'>Newest</SelectItem>
                                <SelectItem value='popular'>Most Popular</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>

            <Tabs defaultValue='community' className='w-full'>
                <TabsList className='mb-6 grid w-full grid-cols-2'>
                    <TabsTrigger value='community'>Community Decks</TabsTrigger>
                    <TabsTrigger value='my-decks'>My Public Decks</TabsTrigger>
                </TabsList>

                <TabsContent value='community'>
                    {isLoading ? (
                        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
                            {Array(6)
                                .fill(0)
                                .map((_, i) => (
                                    <DeckCardSkeleton key={i} />
                                ))}
                        </div>
                    ) : filteredCommunityDecks.length > 0 ? (
                        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
                            {filteredCommunityDecks.map(deck => (
                                <DeckCard key={deck.id} deck={deck} onView={handleViewDeck} />
                            ))}
                        </div>
                    ) : (
                        <div className='flex flex-col items-center justify-center rounded-lg border border-dashed py-12'>
                            <BookOpen className='mb-2 h-12 w-12 text-gray-300' />
                            <h3 className='text-lg font-medium'>No community decks found</h3>
                            <p className='text-sm text-gray-500'>
                                {searchQuery
                                    ? 'Try a different search term'
                                    : 'Be the first to share a deck with the community!'}
                            </p>
                        </div>
                    )}
                </TabsContent>

                <TabsContent value='my-decks'>
                    {isLoading ? (
                        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
                            {Array(3)
                                .fill(0)
                                .map((_, i) => (
                                    <DeckCardSkeleton key={i} />
                                ))}
                        </div>
                    ) : filteredUserDecks.length > 0 ? (
                        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
                            {filteredUserDecks.map(deck => (
                                <DeckCard key={deck.id} deck={deck} onView={handleViewDeck} />
                            ))}
                        </div>
                    ) : (
                        <div className='flex flex-col items-center justify-center rounded-lg border border-dashed py-12'>
                            <BookOpen className='mb-2 h-12 w-12 text-gray-300' />
                            <h3 className='text-lg font-medium'>
                                You haven't shared any decks yet
                            </h3>
                            <p className='text-sm text-gray-500'>
                                Share your decks with the community by making them public in your
                                deck settings.
                            </p>
                            <Button className='mt-4'>Share a Deck</Button>
                        </div>
                    )}
                </TabsContent>
            </Tabs>

            {selectedDeck && (
                <DeckDetailModal
                    deck={selectedDeck}
                    onClose={() => setSelectedDeck(null)}
                    onRate={handleRateDeck}
                    userRating={userRating}
                    isLoading={isRatingLoading}
                />
            )}
        </div>
    );
}
