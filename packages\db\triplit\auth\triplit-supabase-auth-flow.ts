import { schema } from '../schema';
import { TriplitClient } from '@triplit/client';
import { supabase } from './supabase';

// Initialize Triplit client without a token
// The token will be set when a user signs in via the startSession method
export const triplit = new TriplitClient({
    schema,
    serverUrl: 'https://9700f1a8-dc7d-457f-978a-65add3ecbc55.triplit.io',
    // Dont change token to process or .env, leave it here!
    token: '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
});

export function getSessionAndSubscribe() {
    console.log('--getSessionAndSubscribe--');

    // Since we're using a service token, we don't need to start a session
    // But we'll still check for an existing Supabase session to update the useAuth store
    const checkCurrentSession = async () => {
        const {
            data: { session }
        } = await supabase.auth.getSession();
        if (session) {
            console.log('Found existing session');
            // We don't need to call triplit.startSession since we're using a service token
            // This would override our service token with a user token
        } else {
            console.log('No existing session found');
        }
    };

    // Check current session immediately
    checkCurrentSession();

    // Set up subscription for future auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
        console.log(`Auth state changed: ${event}`);

        // We don't need to update the Triplit session since we're using a service token
        // Just log the events for debugging
        switch (event) {
            case 'INITIAL_SESSION':
            case 'SIGNED_IN':
                if (session) {
                    console.log('User signed in');
                    // We don't need to call triplit.startSession since we're using a service token
                }
                break;
            case 'SIGNED_OUT':
                console.log('User signed out');
                break;
            case 'TOKEN_REFRESHED':
                console.log('Token refreshed');
                break;
        }
    });

    return authListener.subscription.unsubscribe;
}
