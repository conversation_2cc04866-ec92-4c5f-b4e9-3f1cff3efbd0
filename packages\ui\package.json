{"name": "@repo/ui", "version": "0.0.0", "private": true, "type": "module", "exports": {"./styles.scss": "./styles/main.scss", "./button": "./ui/components/button.tsx", "./utils": "./lib/utils.ts", ".": "./export.ts"}, "scripts": {"shadcn": "pnpm dlx shadcn@latest", "lint": "run-s eslint stylelint", "eslint": "eslint '**/*.{js,ts,jsx,tsx}' --fix", "stylelint": "stylelint '**/*.{css,scss}' --fix", "format": "prettier --write .", "storybook": "storybook dev -p 6006"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@repo/api": "workspace:*", "@repo/db": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/stylelint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/utils": "workspace:*", "@repo/vite-config": "workspace:*", "@storybook/addon-essentials": "^8.4.6", "@storybook/addon-interactions": "^8.4.6", "@storybook/addon-onboarding": "^8.4.6", "@storybook/blocks": "^8.4.6", "@storybook/react": "^8.4.6", "@storybook/react-vite": "^8.4.6", "@storybook/test": "^8.4.6", "@storybook/types": "^8.4.6", "@types/react": "^19.1.0", "eslint-plugin-storybook": "^0.11.1", "jsdom": "^25.0.1", "npm-run-all2": "^7.0.1", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "react-docgen-typescript": "^2.2.2", "sass": "^1.81.0", "storybook": "^8.4.6", "tailwindcss": "^3.4.15", "unplugin-auto-import": "^0.18.6", "vite": "^5.4.11"}, "peerDependencies": {"react": ">=19.1.0", "react-dom": ">=19.1.0", "typescript": ">=5.7.2"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.1.3", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@triplit/react": "^1.0.27", "@xyflow/react": "^12.5.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.8.0", "input-otp": "^1.4.2", "katex": "^0.16.10", "lucide-react": "^0.464.0", "motion": "^12.9.4", "next-themes": "^0.4.6", "react-day-picker": "8.10.1", "react-hook-form": "^7.55.0", "react-markdown": "^10.0.0", "react-resizable-panels": "^2.1.7", "react-router": "^7.5.0", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.15.2", "rehype-katex": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^2.5.5", "tiptap-markdown": "^0.8.10", "ts-fsrs": "^4.0.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}