import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardTitle, CardDescription } from '@repo/ui';
import {
    TbDatabase,
    TbPuzzle,
    TbCalendarTime,
    TbCloudUpload,
    TbFileInvoice,
    TbBrain,
    TbChartBar,
    TbBrandOpenai,
    TbHeadset,
    TbRocket,
    TbDeviceLaptop
} from 'react-icons/tb';

// Feature comparison data
interface FeatureComparison {
    category: string;
    features: {
        name: string;
        description: string;
        free: boolean | string;
        pro: boolean | string;
        icon: React.ReactNode;
    }[];
}

// Define feature comparison data
const featureComparisons: FeatureComparison[] = [
    {
        category: 'Core Features',
        features: [
            {
                name: 'Decks',
                description: 'Create and manage flashcard decks',
                free: 'Up to 5',
                pro: 'Unlimited',
                icon: <TbDatabase className='h-5 w-5 text-blue-500' />
            },
            {
                name: 'Flashcard Types',
                description: 'Different types of flashcards for varied learning',
                free: 'Basic only',
                pro: 'All types',
                icon: <TbPuzzle className='h-5 w-5 text-purple-500' />
            },
            {
                name: 'Spaced Repetition',
                description: 'Algorithm for optimal learning intervals',
                free: 'Standard',
                pro: 'Advanced FSRS',
                icon: <TbCalendarTime className='h-5 w-5 text-green-500' />
            },
            {
                name: 'Storage',
                description: 'Space for your learning materials',
                free: '100MB',
                pro: '10GB',
                icon: <TbCloudUpload className='h-5 w-5 text-amber-500' />
            }
        ]
    },
    {
        category: 'Advanced Features',
        features: [
            {
                name: 'Documents',
                description: 'Upload and study from documents',
                free: 'Up to 3',
                pro: 'Unlimited',
                icon: <TbFileInvoice className='h-5 w-5 text-red-500' />
            },
            {
                name: 'Concept Maps',
                description: 'Visual learning with concept maps',
                free: 'Basic maps',
                pro: 'Advanced maps with AI generation',
                icon: <TbBrain className='h-5 w-5 text-indigo-500' />
            },
            {
                name: 'Analytics',
                description: 'Track your learning progress',
                free: 'Basic stats',
                pro: 'Comprehensive insights',
                icon: <TbChartBar className='h-5 w-5 text-blue-500' />
            },
            {
                name: 'AI Features',
                description: 'AI-powered learning tools',
                free: false,
                pro: true,
                icon: <TbBrandOpenai className='h-5 w-5 text-green-500' />
            }
        ]
    },
    {
        category: 'Support & Access',
        features: [
            {
                name: 'Customer Support',
                description: 'Get help when you need it',
                free: 'Community',
                pro: 'Priority',
                icon: <TbHeadset className='h-5 w-5 text-amber-500' />
            },
            {
                name: 'New Features',
                description: 'Access to new features',
                free: 'Standard',
                pro: 'Early access',
                icon: <TbRocket className='h-5 w-5 text-purple-500' />
            },
            {
                name: 'Sync Across Devices',
                description: 'Use on multiple devices',
                free: true,
                pro: true,
                icon: <TbDeviceLaptop className='h-5 w-5 text-blue-500' />
            }
        ]
    }
];

export default function BillingFeatureComparison() {
    return (
        <Card className='mt-8'>
            <CardHeader>
                <CardTitle>Feature Comparison</CardTitle>
                <CardDescription>Compare features between Free and Pro plans</CardDescription>
            </CardHeader>
            <CardContent>
                <div className='space-y-8'>
                    {featureComparisons.map((category, idx) => (
                        <div key={idx}>
                            <h3 className='mb-4 text-lg font-medium'>{category.category}</h3>
                            <div className='space-y-4'>
                                {category.features.map((feature, featureIdx) => (
                                    <div
                                        key={featureIdx}
                                        className='grid grid-cols-12 items-center gap-4 rounded-lg border p-4'
                                    >
                                        <div className='col-span-1'>{feature.icon}</div>
                                        <div className='col-span-5'>
                                            <h4 className='font-medium'>{feature.name}</h4>
                                            <p className='text-sm text-gray-500'>
                                                {feature.description}
                                            </p>
                                        </div>
                                        <div className='col-span-3 text-center'>
                                            {typeof feature.free === 'boolean' ? (
                                                feature.free ? (
                                                    <span className='text-green-500'>✓</span>
                                                ) : (
                                                    <span className='text-red-500'>✗</span>
                                                )
                                            ) : (
                                                <span>{feature.free}</span>
                                            )}
                                        </div>
                                        <div className='col-span-3 text-center'>
                                            {typeof feature.pro === 'boolean' ? (
                                                feature.pro ? (
                                                    <span className='text-green-500'>✓</span>
                                                ) : (
                                                    <span className='text-red-500'>✗</span>
                                                )
                                            ) : (
                                                <span className='font-medium text-amber-600'>
                                                    {feature.pro}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
}
