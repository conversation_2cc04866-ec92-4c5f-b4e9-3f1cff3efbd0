import { TbNorthStar, TbSwitch2 } from 'react-icons/tb';
import { Search, BookOpen } from 'lucide-react';
import { Input, cn } from '@repo/ui';
import React, { useState, useEffect, useCallback } from 'react';
import { ActionMenu } from './components/ActionMenu';
import { ConceptSuggestions } from './components/ConceptSuggestions';
import { ConceptSuggestion, useApiStore } from '@repo/api';
import { useDueReviews } from '@repo/db';
import { useGlobalStore } from '@repo/utils';

interface FloatingMenuButtonWrapperProps {
    children: React.ReactNode;
    className?: string;
    disabled?: boolean;
    onClick?: () => void;
}

interface FloatingMenuProps {
    onAddConcepts?: (concepts: ConceptSuggestion[], sourceNodeId: string) => void;
}

export function FloatingMenu({ onAddConcepts }: FloatingMenuProps) {
    const [activeMenu, setActiveMenu] = useState<string | null>(null);
    const [sourceNode, setSourceNode] = useState<{ nodeId: string; conceptTitle: string } | null>(
        null
    );
    const setIsReviewActive = useGlobalStore(state => state.setIsReviewActive);

    // Get API store state
    const suggestions = useApiStore(state => state.suggestions);
    const isLoading = useApiStore(state => state.isLoading);
    const error = useApiStore(state => state.error);
    const sourceConceptTitle = useApiStore(state => state.sourceConceptTitle);
    const requestType = useApiStore(state => state.requestType);

    // Clear API store function
    const clearApiStore = useCallback(() => {
        const apiStore = useApiStore.getState();
        apiStore.setSuggestions([]);
        apiStore.setError(null);
    }, []);

    // Reset state when component unmounts or when activeMenu changes
    useEffect(() => {
        return () => {
            if (activeMenu === null) {
                clearApiStore();
            }
        };
    }, [activeMenu, clearApiStore]);

    // Update sourceNode when sourceConceptTitle changes
    useEffect(() => {
        if (sourceConceptTitle) {
            const apiStore = useApiStore.getState();
            const nodeId = apiStore.currentNodeId || 'current-node';

            setSourceNode({
                nodeId,
                conceptTitle: sourceConceptTitle
            });
        }
    }, [sourceConceptTitle]);

    // Show findConcepts menu when suggestions are loaded (only for concepts request type)
    useEffect(() => {
        if ((isLoading || suggestions.length > 0) && requestType === 'concepts') {
            setActiveMenu('findConcepts');
        }
    }, [isLoading, suggestions.length, requestType]);

    // Button handlers
    const handleSearchClick = useCallback(() => {
        console.log('[FloatingMenu] Search clicked');
    }, []);

    const handleFindConceptsClick = useCallback(() => {
        if (activeMenu === 'findConcepts') {
            clearApiStore();
        }
        setActiveMenu(activeMenu === 'findConcepts' ? null : 'findConcepts');
    }, [activeMenu, clearApiStore]);

    const handleGenerateAutoEdgesClick = useCallback(() => {
        setActiveMenu(activeMenu === 'generateAutoEdges' ? null : 'generateAutoEdges');
    }, [activeMenu]);

    const handleStartReviewClick = useCallback(() => {
        // Set review mode active in global store
        setIsReviewActive(true);
    }, [setIsReviewActive]);

    const handleCancelClick = useCallback(() => {
        clearApiStore();
        setActiveMenu(null);
    }, [clearApiStore]);

    const handleCloseReview = useCallback(() => {
        setIsReviewActive(false);
    }, [setIsReviewActive]);

    const handleSelectConcepts = useCallback(
        (selectedConcepts: ConceptSuggestion[]) => {
            console.log('[FloatingMenu] Selected concepts:', selectedConcepts);

            if (onAddConcepts && sourceNode && selectedConcepts.length > 0) {
                // Make sure we have valid concepts with IDs
                const validConcepts = selectedConcepts.filter(concept => concept && concept.id);

                if (validConcepts.length > 0) {
                    onAddConcepts(validConcepts, sourceNode.nodeId);
                    console.log(
                        `[FloatingMenu] Adding ${validConcepts.length} concepts to node ${sourceNode.nodeId}`
                    );
                }
            }

            // Clear state and close menu
            clearApiStore();
            setActiveMenu(null);
        },
        [clearApiStore, onAddConcepts, sourceNode]
    );

    // Render menu content based on active menu
    const renderMenuContent = useCallback(() => {
        if (!activeMenu) return null;

        switch (activeMenu) {
            case 'findConcepts':
                // Only show concept suggestions if the request type is 'concepts'
                if ((isLoading || suggestions.length > 0 || error) && requestType === 'concepts') {
                    return {
                        title: 'Find Concepts',
                        description: sourceNode
                            ? `Related concepts for "${sourceNode.conceptTitle}"`
                            : 'Related concepts',
                        content: (
                            <ConceptSuggestions
                                suggestions={suggestions}
                                isLoading={isLoading}
                                error={error}
                                onSelectConcepts={handleSelectConcepts}
                                onCancel={handleCancelClick}
                                sourceNodeTitle={sourceNode?.conceptTitle}
                            />
                        )
                    };
                }

                return {
                    title: 'Find Concepts',
                    description: 'Select nodes to find related concepts',
                    content: (
                        <div className='mt-2 flex flex-col gap-2'>
                            <div className='rounded-md bg-gray-50 p-2'>
                                <p className='text-xs'>Select a node to find related concepts</p>
                            </div>
                            <div className='flex gap-2'>
                                <button className='rounded-md bg-lime-100 px-3 py-1 text-xs font-medium hover:bg-lime-200'>
                                    Find Related
                                </button>
                                <button
                                    className='rounded-md bg-gray-100 px-3 py-1 text-xs font-medium hover:bg-gray-200'
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    )
                };

            case 'generateAutoEdges':
                return {
                    title: 'Generate Auto Edges',
                    description: 'Automatically create connections between related nodes',
                    content: (
                        <div className='mt-2 flex flex-col gap-2'>
                            <div className='rounded-md bg-gray-50 p-2'>
                                <p className='text-xs'>Select nodes to connect automatically</p>
                            </div>
                            <div className='flex gap-2'>
                                <button className='rounded-md bg-red-100 px-3 py-1 text-xs font-medium hover:bg-red-200'>
                                    Connect Selected
                                </button>
                                <button
                                    className='rounded-md bg-gray-100 px-3 py-1 text-xs font-medium hover:bg-gray-200'
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    )
                };

            case 'startReview':
                return {
                    title: 'Start Review',
                    description: 'Begin reviewing flashcards for selected concepts',
                    content: (
                        <div className='mt-2 flex flex-col gap-2'>
                            <div className='rounded-md bg-gray-50 p-2'>
                                <p className='text-xs'>Select concepts to review</p>
                            </div>
                            <div className='flex gap-2'>
                                <button className='rounded-md bg-green-100 px-3 py-1 text-xs font-medium hover:bg-green-200'>
                                    Start Review
                                </button>
                                <button
                                    className='rounded-md bg-gray-100 px-3 py-1 text-xs font-medium hover:bg-gray-200'
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    )
                };

            default:
                return {
                    title: '',
                    description: '',
                    content: null
                };
        }
    }, [
        activeMenu,
        isLoading,
        suggestions,
        error,
        sourceNode,
        requestType,
        handleSelectConcepts,
        handleCancelClick
    ]);

    const menuInfo = activeMenu ? renderMenuContent() : null;

    return (
        <div className='mb-[40px] flex flex-col rounded-md border border-gray-200 bg-white drop-shadow-lg'>
            {menuInfo && (
                <ActionMenu
                    isOpen={activeMenu !== null}
                    title={menuInfo.title}
                    description={menuInfo.description}
                >
                    {menuInfo.content}
                </ActionMenu>
            )}

            <div className='flex space-x-1 p-1'>
                <FloatingMenuButtonWrapper onClick={handleSearchClick}>
                    <FloatingMenuSearch />
                </FloatingMenuButtonWrapper>
                <FloatingMenuButtonWrapper
                    className={cn(
                        'bg-lime-200',
                        activeMenu === 'findConcepts' && 'ring-2 ring-lime-400'
                    )}
                    disabled={false}
                    onClick={handleFindConceptsClick}
                >
                    <FindConceptsButton disabled={false} />
                </FloatingMenuButtonWrapper>
                <FloatingMenuButtonWrapper
                    className={cn(
                        'bg-red-200',
                        activeMenu === 'generateAutoEdges' && 'ring-2 ring-red-400'
                    )}
                    disabled={false}
                    onClick={handleGenerateAutoEdgesClick}
                >
                    <GenerateAutoEdges disabled={false} />
                </FloatingMenuButtonWrapper>
                <FloatingMenuButtonWrapper
                    className={cn(
                        'bg-green-200',
                        activeMenu === 'startReview' && 'ring-2 ring-green-400'
                    )}
                    disabled={false}
                    onClick={handleStartReviewClick}
                >
                    <StartReviewButton disabled={false} />
                </FloatingMenuButtonWrapper>
            </div>
        </div>
    );
}

function FloatingMenuSearch() {
    return (
        <div className='relative w-[200px] border-none'>
            <Search className='absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400' />
            <Input placeholder='Search...' className='h-[40px] pl-8' />
        </div>
    );
}

interface ActionButtonProps {
    disabled?: boolean;
}

function FindConceptsButton({ disabled }: ActionButtonProps) {
    return (
        <div
            className={cn(
                'flex h-[36px] w-[36px] items-center justify-center',
                disabled && 'opacity-50'
            )}
        >
            <TbNorthStar className='h-4 w-4' />
        </div>
    );
}

function GenerateAutoEdges({ disabled }: ActionButtonProps) {
    return (
        <div
            className={cn(
                'flex max-h-[36px] max-w-[36px] items-center justify-center text-blue-600',
                disabled && 'opacity-50'
            )}
        >
            <TbSwitch2 className='h-4 w-4' />
        </div>
    );
}

function StartReviewButton({ disabled }: ActionButtonProps) {
    // Use the selector pattern to only subscribe to the activeDeckId
    const activeDeckId = useGlobalStore(state => state.activeDeckId);
    const { dueReviewsCount, isLoading } = useDueReviews(activeDeckId);

    return (
        <div
            className={cn(
                'flex h-[40px] w-[120px] items-center justify-center gap-2',
                disabled && 'opacity-50'
            )}
        >
            <BookOpen className='h-4 w-4' />
            <span className='text-sm font-medium'>
                Review {!isLoading && dueReviewsCount > 0 && `(${dueReviewsCount})`}
            </span>
        </div>
    );
}

function FloatingMenuButtonWrapper({
    children,
    className,
    disabled = false,
    onClick
}: FloatingMenuButtonWrapperProps) {
    return (
        <div
            className={cn(
                'rounded-sm transition-colors hover:cursor-pointer',
                disabled && 'pointer-events-none cursor-not-allowed opacity-50',
                className
            )}
            onClick={disabled ? undefined : onClick}
        >
            {children}
        </div>
    );
}
