import React, { useRef, useEffect, useState } from 'react';
import { Loader2, X } from 'lucide-react';
import { Button, Skeleton } from '@repo/ui';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';
import { motion, AnimatePresence } from 'framer-motion';
import './tiptap-editor.css';
import { TiptapInlineEditor } from './TiptapInlineEditor';

interface NodeSectionsProps {
    sections: Record<string, SectionConfig>;
    loadingSections: Record<string, boolean>;
    generationError: Record<string, string>;
    updateNodeData: (data: Record<string, any>) => void;
    generateSectionContentWithLLM: (section: SectionConfig) => Promise<void>;
}

export const NodeSections: React.FC<NodeSectionsProps> = ({
    sections,
    loadingSections,
    generationError,
    updateNodeData,
    generateSectionContentWithLLM
}) => {
    const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});
    const [editingSectionLabelId, setEditingSectionLabelId] = useState<string | null>(null);
    const [editingLabelValue, setEditingLabelValue] = useState('');

    const editingLabelInputRef = useRef<HTMLInputElement>(null);

    const toggleSectionCollapse = (sectionId: string) => {
        setCollapsedSections(prev => {
            const newState = { ...prev };

            Object.keys(sections).forEach(id => {
                newState[id] = id === sectionId ? !prev[sectionId] : true;
            });

            return newState;
        });
    };

    // Section label editing functions
    const startEditingSectionLabel = (sectionId: string, currentLabel: string) => {
        setEditingSectionLabelId(sectionId);
        setEditingLabelValue(currentLabel);
    };

    const handleSectionLabelChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEditingLabelValue(event.target.value);
    };

    const saveSectionLabelEdit = () => {
        if (editingSectionLabelId) {
            const finalLabel = editingLabelValue.trim() || 'Untitled Section'; // Ensure not empty
            const updatedSections = {
                ...sections,
                [editingSectionLabelId]: {
                    ...sections[editingSectionLabelId],
                    label: finalLabel
                }
            };
            updateNodeData({ sections: updatedSections });
            setEditingSectionLabelId(null);
            setEditingLabelValue('');
        }
    };

    // Retry generating content for a section
    const retryGenerateContent = (sectionId: string) => {
        const section = sections[sectionId];
        if (section) {
            generateSectionContentWithLLM(section);
        } else {
            console.error(`Cannot retry generation: Section ${sectionId} not found`);
        }
    };

    // Remove a section from the node
    const removeSection = (sectionId: string) => {
        // Create a copy of the sections object without the section to remove
        const updatedSections = { ...sections };
        delete updatedSections[sectionId];

        // Update the node data with the updated sections
        updateNodeData({ sections: updatedSections });
    };

    useEffect(() => {
        // Focus the label input when editing starts
        if (editingSectionLabelId && editingLabelInputRef.current) {
            editingLabelInputRef.current.focus();
            editingLabelInputRef.current.select(); // Select text
        }
    }, [editingSectionLabelId]);

    return (
        <div className='mb-4 space-y-1.5 text-sm'>
            {typeof sections === 'object' &&
                sections !== null &&
                Object.values(sections as Record<string, SectionConfig>).map(
                    (section: SectionConfig) => {
                        const isEditingLabel = editingSectionLabelId === section.id;
                        const isCollapsed = collapsedSections[section.id] && section.collapsible;

                        return (
                            <div
                                key={section.id}
                                className='group rounded border border-gray-100 bg-gray-50/50 p-2'
                            >
                                <motion.div
                                    className='flex cursor-pointer items-center justify-between'
                                    onClick={() =>
                                        section.collapsible && toggleSectionCollapse(section.id)
                                    }
                                >
                                    <div className='flex items-center gap-1.5 text-xs font-medium text-gray-600'>
                                        {/* Allow clicking icon to maybe change icon later? */}
                                        <span style={{ display: 'inline-block' }}>
                                            {section.icon}
                                        </span>
                                        {editingSectionLabelId === section.id ? (
                                            <input
                                                ref={editingLabelInputRef}
                                                type='text'
                                                value={editingLabelValue}
                                                onChange={handleSectionLabelChange}
                                                onBlur={saveSectionLabelEdit}
                                                onClick={e => e.stopPropagation()}
                                                onKeyDown={e => {
                                                    if (e.key === 'Enter') e.currentTarget.blur();
                                                }}
                                                className='w-full rounded-sm bg-gray-100 px-1 py-0 text-xs font-medium text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-400'
                                                placeholder='Section Title'
                                            />
                                        ) : (
                                            <span
                                                className='cursor-text rounded px-1 py-0 hover:bg-gray-100'
                                                onDoubleClick={e => {
                                                    e.stopPropagation();
                                                    startEditingSectionLabel(
                                                        section.id,
                                                        section.label
                                                    );
                                                }}
                                                onClick={e => e.stopPropagation()}
                                            >
                                                {section.label}
                                            </span>
                                        )}
                                    </div>
                                    <div className='flex items-center gap-1'>
                                        <Button
                                            variant='ghost'
                                            size='sm'
                                            onClick={e => {
                                                e.stopPropagation();
                                                removeSection(section.id);
                                            }}
                                            className='h-5 w-5 p-0 text-gray-400 opacity-0 transition-opacity duration-200 hover:text-red-500 group-hover:opacity-100'
                                            title='Remove section'
                                        >
                                            <X size={12} />
                                        </Button>
                                    </div>
                                </motion.div>
                                <div className='mt-1.5 overflow-hidden pl-1 text-sm text-gray-800'>
                                    <AnimatePresence>
                                        {!isCollapsed && (
                                            <motion.div
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: 'auto' }}
                                                exit={{ opacity: 0, height: 0 }}
                                                transition={{
                                                    duration: 0.1,
                                                    ease: [0.04, 0.62, 0.23, 0.98]
                                                }}
                                            >
                                                {loadingSections[section.id] ? (
                                                    <div className='flex flex-col space-y-2 py-1'>
                                                        <div className='flex items-center space-x-2'>
                                                            <Loader2 className='h-3 w-3 animate-spin text-gray-400' />
                                                            <span className='text-xs text-gray-500'>
                                                                Generating content...
                                                            </span>
                                                        </div>
                                                        <Skeleton className='h-3 w-full' />
                                                        <Skeleton className='h-3 w-4/5' />
                                                        <Skeleton className='h-3 w-3/5' />
                                                    </div>
                                                ) : generationError[section.id] ? (
                                                    <div
                                                        onClick={() => {
                                                            console.log(
                                                                '[SectionClickBug] Retrying generation for section:',
                                                                section.id
                                                            );
                                                            retryGenerateContent(section.id);
                                                        }}
                                                        className='min-h-[20px] cursor-pointer rounded p-0 text-xs italic text-red-400 hover:bg-gray-100/50'
                                                        style={{ lineHeight: '1.5' }}
                                                    >
                                                        Failed to generate content. Click to try
                                                        again.
                                                    </div>
                                                ) : (
                                                    <TiptapInlineEditor
                                                        initialValue={section.value}
                                                        onSave={newValue => {
                                                            console.log(
                                                                '[SectionClickBug] Saving section content:',
                                                                newValue
                                                            );
                                                            const updatedSections = {
                                                                ...sections,
                                                                [section.id]: {
                                                                    ...sections[section.id],
                                                                    value: newValue
                                                                }
                                                            };
                                                            updateNodeData({
                                                                sections: updatedSections
                                                            });
                                                        }}
                                                        placeholder='Click to edit this section...'
                                                    />
                                                )}
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </div>
                            </div>
                        );
                    }
                )}
        </div>
    );
};
