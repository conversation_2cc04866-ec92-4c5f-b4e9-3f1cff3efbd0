import { Outlet, Link, useLocation } from 'react-router';
import { cn, Button } from '@repo/ui';

export default function MarketingLayout() {
    console.log('[apps/web/src/modules/marketing/routes/landing.tsx:MarketingLayout]');
    const location = useLocation();

    // Only show the marketing navbar and Outlet if not on /app or its children
    const isAppRoute = location.pathname === '/app' || location.pathname.startsWith('/app/');

    if (isAppRoute) {
        // If on /app or any /app/* route, render nothing (or could render <Outlet /> if needed)
        return null;
    }

    const navigation = [
        { name: 'Home', href: '/' },
        { name: 'Features', href: '/features' },
        { name: 'Pricing', href: '/pricing' },
        { name: 'About', href: '/about' }
    ];

    const isAuthenticated = false;

    return (
        <div className='flex flex-col'>
            <div className='relative flex min-h-screen flex-col'>
                <header className='sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
                    <div className='container flex h-14 items-center'>
                        <div className='mr-4 hidden md:flex'>
                            <Link to='/' className='mr-6 flex items-center space-x-2'>
                                <span className='hidden font-bold sm:inline-block'>Unisono</span>
                            </Link>
                            <nav className='flex items-center space-x-6 text-sm font-medium'>
                                {navigation.map(item => (
                                    <Link
                                        key={item.href}
                                        to={item.href}
                                        className={cn(
                                            'transition-colors hover:text-foreground/80',
                                            location.pathname === item.href
                                                ? 'text-foreground'
                                                : 'text-foreground/60'
                                        )}
                                    >
                                        {item.name}
                                    </Link>
                                ))}
                            </nav>
                        </div>
                        <div className='flex flex-1 items-center justify-between space-x-2 md:justify-end'>
                            <div className='flex items-center'>
                                <nav className='flex items-center space-x-2'>
                                    {isAuthenticated ? (
                                        <>
                                            <Button asChild variant='ghost' size='sm'>
                                                <Link to='/app/dashboard'>Dashboard</Link>
                                            </Button>
                                            <Button asChild size='sm'>
                                                <Link to='/login?signout=true'>Sign Out</Link>
                                            </Button>
                                        </>
                                    ) : (
                                        <>
                                            <Button asChild variant='ghost' size='sm'>
                                                <Link to='/login'>Login</Link>
                                            </Button>
                                            <Button asChild size='sm'>
                                                <Link to='/register'>Get Started</Link>
                                            </Button>
                                        </>
                                    )}
                                </nav>
                            </div>
                        </div>
                    </div>
                </header>
                <main className='flex-1'>
                    <Outlet />
                </main>
            </div>
        </div>
    );
}
