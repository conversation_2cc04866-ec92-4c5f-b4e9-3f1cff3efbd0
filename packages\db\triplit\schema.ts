import { Schema as S } from '@triplit/client';

// Define schema for Triplit using the 1.0 format
export const schema = S.Collections({
    // Decks collection
    decks: {
        schema: S.Schema({
            id: S.Id(),
            name: S.String(),
            learning_outcome: S.String(),
            is_public: <PERSON><PERSON>(),
            created_at: S.String(),
            updated_at: S.String(),
            version: S.Number(),
            user_id: S.String(),
            type: S.String(),
            new_concepts_per_day: S.Number(),
            deadline: S.String(),
            priority: S.Number(),
            folder_id: S.Optional(S.String()),
            nodes: S.String({ default: '[]' }),
            edges: S.String({ default: '[]' }),
            language: S.Optional(S.String())
        }),
        permissions: {
            anonymous: {
                read: { filter: [['is_public', '=', true]] }
            },
            authenticated: {
                read: { filter: [true] }, // Allow authenticated users to read all decks
                insert: { filter: [true] }, // Allow authenticated users to insert decks
                update: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to update their own decks
                delete: { filter: [['user_id', '=', '$token.sub']] } // Only allow users to delete their own decks
            },
            service: {
                read: { filter: [true] },
                insert: { filter: [true] },
                update: { filter: [true] },
                delete: { filter: [true] }
            }
        }
    },
    // Folders collection
    folders: {
        schema: S.Schema({
            id: S.Id(),
            name: S.String(),
            created_at: S.String(),
            updated_at: S.String(),
            user_id: S.String()
        }),
        permissions: {
            authenticated: {
                read: { filter: [true] }, // Allow authenticated users to read all folders
                insert: { filter: [true] }, // Allow authenticated users to insert folders
                update: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to update their own folders
                delete: { filter: [['user_id', '=', '$token.sub']] } // Only allow users to delete their own folders
            },
            service: {
                read: { filter: [true] },
                insert: { filter: [true] },
                update: { filter: [true] },
                delete: { filter: [true] }
            }
        }
    },

    // Cards collection
    cards: {
        schema: S.Schema({
            id: S.Id(),
            created_at: S.String(),
            updated_at: S.String(),
            user_id: S.String(),
            card_text: S.String(),
            card_type: S.String(),
            stability: S.Number(),
            difficulty: S.Number(),
            elapsed_days: S.Number(),
            scheduled_days: S.Number(),
            reps: S.Number(),
            lapses: S.Number(),
            state: S.String(),
            last_review: S.String(),
            due: S.String(),
            deck_id: S.String(),
            concept_id: S.String(),
            answer_time_ms: S.Optional(S.Number()),
            answer_quality: S.Optional(S.Number()),
            last_answer_quality: S.Optional(S.Number()),
            concept_due: S.Optional(S.String())
        }),
        permissions: {
            authenticated: {
                read: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to read their own cards
                insert: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to insert their own cards
                update: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to update their own cards
                delete: { filter: [['user_id', '=', '$token.sub']] } // Only allow users to delete their own cards
            },
            service: {
                read: { filter: [true] },
                insert: { filter: [true] },
                update: { filter: [true] },
                delete: { filter: [true] }
            }
        }
    },
    // Review history collection
    reviewHistory: {
        schema: S.Schema({
            id: S.Id(),
            created_at: S.String(),
            user_id: S.String(),
            card_id: S.String(),
            concept_id: S.String(),
            grade: S.Number(),
            answer_time_ms: S.Number(),
            answer_quality: S.Optional(S.Number()),
            scheduled_days: S.Number(),
            review_type: S.String()
        }),
        permissions: {
            authenticated: {
                read: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to read their own review history
                insert: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to insert their own review history
                update: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to update their own review history
                delete: { filter: [['user_id', '=', '$token.sub']] } // Only allow users to delete their own review history
            },
            service: {
                read: { filter: [true] },
                insert: { filter: [true] },
                update: { filter: [true] },
                delete: { filter: [true] }
            }
        }
    },
    // Concept scheduling collection
    conceptScheduling: {
        schema: S.Schema({
            id: S.Id(),
            created_at: S.String(),
            updated_at: S.String(),
            user_id: S.String(),
            concept_id: S.String(),
            deck_id: S.String(),
            due: S.String(),
            avg_stability: S.Number(),
            avg_difficulty: S.Number(),
            total_cards: S.Number(),
            mastered_cards: S.Number(),
            last_review: S.Optional(S.String())
        }),
        permissions: {
            authenticated: {
                read: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to read their own concept scheduling
                insert: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to insert their own concept scheduling
                update: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to update their own concept scheduling
                delete: { filter: [['user_id', '=', '$token.sub']] } // Only allow users to delete their own concept scheduling
            },
            service: {
                read: { filter: [true] },
                insert: { filter: [true] },
                update: { filter: [true] },
                delete: { filter: [true] }
            }
        }
    },
    // Documents collection
    documents: {
        schema: S.Schema({
            id: S.Id(),
            name: S.String(),
            type: S.String(),
            created_at: S.String(),
            updated_at: S.String(),
            user_id: S.String(),
            storage_path: S.String(),
            folder_id: S.Optional(S.String())
        }),
        permissions: {
            authenticated: {
                read: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to read their own documents
                insert: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to insert their own documents
                update: { filter: [['user_id', '=', '$token.sub']] }, // Only allow users to update their own documents
                delete: { filter: [['user_id', '=', '$token.sub']] } // Only allow users to delete their own documents
            },
            service: {
                read: { filter: [true] },
                insert: { filter: [true] },
                update: { filter: [true] },
                delete: { filter: [true] }
            }
        }
    }
});

// Define TypeScript types for each collection
export type DBSchema = typeof schema;

export type DeckDocType = {
    id: string;
    name: string;
    learning_outcome: string;
    is_public: boolean;
    created_at: string;
    updated_at: string;
    version: number;
    user_id: string;
    type: string;
    new_concepts_per_day: number;
    deadline: string;
    priority: number;
    folder_id?: string | null;
    nodes: string;
    edges: string;
};

export type CardDocType = {
    id: string;
    created_at: string;
    updated_at: string;
    user_id: string;
    card_text: string;
    card_type: string;
    stability: number;
    difficulty: number;
    elapsed_days: number;
    scheduled_days: number;
    reps: number;
    lapses: number;
    state: string;
    last_review: string;
    due: string;
    deck_id: string;
    concept_id: string;
    answer_time_ms?: number;
    answer_quality?: number;
    last_answer_quality?: number;
    concept_due?: string;
};

export type FolderDocType = {
    id: string;
    name: string;
    created_at: string;
    updated_at: string;
    user_id: string;
};

export type ReviewHistoryDocType = {
    id: string;
    created_at: string;
    user_id: string;
    card_id: string;
    concept_id: string;
    grade: number;
    answer_time_ms: number;
    answer_quality?: number;
    scheduled_days: number;
    review_type: string;
};

export type ConceptSchedulingDocType = {
    id: string;
    created_at: string;
    updated_at: string;
    user_id: string;
    concept_id: string;
    deck_id: string;
    due: string;
    avg_stability: number;
    avg_difficulty: number;
    total_cards: number;
    mastered_cards: number;
    last_review?: string;
};

export type DocumentDocType = {
    id: string;
    name: string;
    type: string;
    created_at: string;
    updated_at: string;
    user_id: string;
    storage_path: string;
    folder_id?: string | null;
};

export type UserDocType = {
    id: string;
    email: string;
    password?: string;
    name?: string;
    image?: string;
    role: string;
    emailVerified: boolean;
    createdAt: string;
    updatedAt: string;
};
