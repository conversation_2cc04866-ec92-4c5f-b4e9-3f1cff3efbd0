import { create } from 'zustand';
import { OnConnect } from '@xyflow/react';

// Define the edge store state interface
interface EdgeStoreState {
    // Edge event handlers
    onConnect: OnConnect;
}

export const useEdgeStore = create<EdgeStoreState>((set, get) => ({
    onConnect: connection => {
        console.log('[EdgeStore:onConnect] Creating new edge:', connection);

        // This will be implemented to handle edge connections
        // For now, we'll import the core map store to update edges
        const { useMapStore } = require('../Map/MapStore');
        const mapStore = useMapStore.getState();

        // Get the default edge type
        const defaultEdgeType = Object.keys(mapStore.edgeTypes)[0] || 'default';

        // Create a new edge with the connection
        mapStore.setEdges(prevEdges => [
            ...prevEdges,
            {
                id: `edge-${connection.source}-${connection.target}-${connection.sourceHandle || ''}-${connection.targetHandle || ''}`,
                source: connection.source,
                target: connection.target,
                sourceHandle: connection.sourceHandle,
                targetHandle: connection.targetHandle,
                type: defaultEdgeType,
                data: {
                    edgeType: defaultEdgeType
                }
            }
        ]);

        // Update the map in the database
        mapStore.debouncedUpdateMap();
    }
}));
