'use client';

import * as React from 'react';
import { cn } from '../../export';

interface CircularProgressProps extends React.SVGAttributes<SVGSVGElement> {
    value?: number;
    size?: number;
    strokeWidth?: number;
    showValue?: boolean;
    valueClassName?: string;
}

const CircularProgress = React.forwardRef<SVGSVGElement, CircularProgressProps>(
    (
        {
            className,
            value = 0,
            size = 16,
            strokeWidth = 2,
            showValue = false,
            valueClassName,
            ...props
        },
        ref
    ) => {
        // Ensure value is between 0 and 100
        const normalizedValue = Math.max(0, Math.min(100, value));

        // Calculate circle properties
        const radius = (size - strokeWidth) / 2;
        const circumference = 2 * Math.PI * radius;
        const strokeDashoffset = circumference - (normalizedValue / 100) * circumference;

        return (
            <div className='relative inline-flex items-center justify-center'>
                <svg
                    ref={ref}
                    width={size}
                    height={size}
                    viewBox={`0 0 ${size} ${size}`}
                    className={cn('-rotate-90 transform', className)}
                    {...props}
                >
                    {/* Background circle */}
                    <circle
                        cx={size / 2}
                        cy={size / 2}
                        r={radius}
                        fill='none'
                        stroke='currentColor'
                        strokeWidth={strokeWidth}
                        className='text-gray-200/30'
                    />
                    {/* Progress circle */}
                    <circle
                        cx={size / 2}
                        cy={size / 2}
                        r={radius}
                        fill='none'
                        stroke='currentColor'
                        strokeWidth={strokeWidth}
                        strokeDasharray={circumference}
                        strokeDashoffset={strokeDashoffset}
                        strokeLinecap='round'
                        className='transition-all duration-300 ease-in-out'
                    />
                </svg>
                {showValue && (
                    <div
                        className={cn(
                            'absolute inset-0 flex items-center justify-center text-[8px] font-medium',
                            valueClassName
                        )}
                    >
                        {Math.round(normalizedValue)}%
                    </div>
                )}
            </div>
        );
    }
);

CircularProgress.displayName = 'CircularProgress';

export { CircularProgress };
