import React, { useState } from 'react';
import { Node, Edge, useReactFlow } from '@xyflow/react';
import { Link } from 'lucide-react';
import { EDGE_TYPES } from '@repo/utils';
import {
    Button,
    Popover,
    PopoverTrigger,
    PopoverContent,
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList
} from '@repo/ui';
import { MapNodeData } from '../../../Map/interfaces/MapNodeData';
import { handleNodeSelectForTagging } from './handleNodeSelectForTagging';
import { handleEdgeTypeSelect } from './handleEdgeTypeSelect';
import { handleEdgeBadgeHover } from './handleEdgeBadgeHover';
import { useFilteredNodes } from './useFilteredNodes';

interface NodeTagsListProps {
    id: string;
    showAddBar: boolean;
}

export const NodeTagsList: React.FC<NodeTagsListProps> = ({ id, showAddBar }) => {
    const { getNode, getEdges, setEdges } = useReactFlow();

    const [linkPopoverOpen, setLinkPopoverOpen] = useState(false);
    const [linkSearchQuery, setLinkSearchQuery] = useState('');
    const [selectedNodeToLink, setSelectedNodeToLink] = useState<Node | null>(null);
    const [edgeTypePopoverOpen, setEdgeTypePopoverOpen] = useState(false);

    // Get outgoing edges for tag display
    const allEdges = getEdges();
    const outgoingEdges = allEdges.filter(edge => edge.source === id);

    // Get filtered nodes for link selection
    const filteredNodes = useFilteredNodes(id, linkSearchQuery);

    // Helper: check if edge type is valid
    const isValidEdgeType = (type: string): type is keyof typeof EDGE_TYPES => {
        return type in EDGE_TYPES;
    };

    return (
        <div className='mb-3 mt-2 flex flex-wrap items-center gap-1'>
            {outgoingEdges.map((edge: Edge<{ edgeType?: string }>) => {
                const edgeType = edge.data?.edgeType || edge.type;
                if (!edgeType || !isValidEdgeType(edgeType)) return null;
                const tagStyle = EDGE_TYPES[edgeType];
                const targetNode = getNode(edge.target) as Node<MapNodeData> | undefined;
                return (
                    <div
                        key={edge.id}
                        className={`flex cursor-pointer items-center gap-1 rounded-full px-2 py-0.5 text-[10px] ${tagStyle.color}`}
                        title={`Link (${edgeType}) to: ${targetNode?.data?.conceptTitle || edge.target}`}
                        style={{ background: '#f5f5f5', border: '1px solid #e5e7eb' }}
                        onMouseEnter={() => handleEdgeBadgeHover(edge.id, true, setEdges)}
                        onMouseLeave={() => handleEdgeBadgeHover(edge.id, false, setEdges)}
                    >
                        <span className='text-[10px]'>{tagStyle.icon}</span>
                        <span>{targetNode?.data?.conceptTitle || 'Linked Node'}</span>
                    </div>
                );
            })}
            <Popover open={linkPopoverOpen} onOpenChange={setLinkPopoverOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant='outline'
                        size='sm'
                        className={`flex h-6 items-center gap-1 rounded border border-dashed border-gray-300 px-2 text-xs text-gray-500 transition-opacity duration-300 hover:border-gray-400 hover:text-gray-700 ${
                            showAddBar ? 'opacity-100' : 'pointer-events-none opacity-0'
                        }`}
                        onClick={() => {
                            setLinkSearchQuery('');
                            setLinkPopoverOpen(true);
                        }}
                    >
                        <Link size={12} /> + Link
                    </Button>
                </PopoverTrigger>
                <PopoverContent className='w-[300px] p-0' align='start'>
                    <Command>
                        <CommandInput
                            placeholder='Search concepts to link...'
                            value={linkSearchQuery}
                            onValueChange={setLinkSearchQuery}
                        />
                        <CommandList>
                            <CommandEmpty>No concepts found.</CommandEmpty>
                            <CommandGroup heading='Concepts'>
                                {filteredNodes.map((node: Node<MapNodeData>) => (
                                    <CommandItem
                                        key={node.id}
                                        value={node.id}
                                        onSelect={() =>
                                            handleNodeSelectForTagging(
                                                node.id,
                                                getNode,
                                                setSelectedNodeToLink,
                                                setLinkPopoverOpen,
                                                setEdgeTypePopoverOpen
                                            )
                                        }
                                    >
                                        {node.data?.conceptTitle || 'Untitled Concept'}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {/* Edge Type Selection Popover */}
            <Popover open={edgeTypePopoverOpen} onOpenChange={setEdgeTypePopoverOpen}>
                <PopoverTrigger />
                <PopoverContent className='w-[200px] p-1' align='start'>
                    <Command>
                        <CommandList>
                            <CommandGroup heading='Link Type'>
                                {Object.entries(EDGE_TYPES).map(([typeKey, typeValue]) => (
                                    <CommandItem
                                        key={typeKey}
                                        value={typeKey}
                                        onSelect={() =>
                                            handleEdgeTypeSelect(
                                                typeKey,
                                                selectedNodeToLink,
                                                id,
                                                getNode,
                                                setEdges,
                                                setEdgeTypePopoverOpen,
                                                setSelectedNodeToLink,
                                                setLinkSearchQuery
                                            )
                                        }
                                        className='flex items-center gap-2'
                                        title={typeValue.description}
                                    >
                                        <span>{typeValue.icon}</span>
                                        <span>
                                            {typeKey.charAt(0).toUpperCase() + typeKey.slice(1)}
                                        </span>
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
    );
};
