import { NodeSection, NodeTag, callChatGPT4oMini } from '../openai';
import { generateSection } from '../prompts/generateSection';
import { useGlobalStore } from '@repo/utils';

/**
 * Generates content for a section of a concept node
 * 
 * @param conceptTitle - The title of the concept
 * @param sectionLabel - The label of the section to generate content for
 * @param tags - The tags associated with the concept
 * @param sections - The existing sections of the concept
 * @returns The generated content for the section
 */
export async function generateSectionContent(
  conceptTitle: string,
  sectionLabel: string,
  tags: NodeTag[],
  sections: Record<string, NodeSection>
): Promise<string> {
  try {
    // Get global store data
    const {
      activeDeckName: deckTitle,
      activeDeckLearningOutcome: learningOutcome,
      activeDeckLanguage: language
    } = useGlobalStore.getState();

    // Generate the prompt for the section
    const prompt = generateSection(
      conceptTitle,
      sectionLabel,
      sections,
      deckTitle || undefined,
      learningOutcome || undefined,
      language || 'en'
    );

    // Call the OpenAI API
    const response = await callChatGPT4oMini(prompt, {
      temperature: 0.7,
      maxTokens: 2500
    });

    // Process the response
    const lines = response.content.split('\n');
    let heading = '';
    let content = '';

    if (lines.length > 0) {
      // First line is the heading
      heading = lines[0].trim();

      // Skip the first two lines (heading and blank line) and join the rest
      content = lines.slice(2).join('\n').trim();

      console.log('Generated section heading:', heading);
      console.log('Generated section content:', content);
    } else {
      // Fallback if the format is not as expected
      content = response.content;
    }

    // Return the content
    if (!content) {
      throw new Error('No content was generated');
    }

    return content;
  } catch (error) {
    console.error('Error generating section content:', error);
    throw error;
  }
}
