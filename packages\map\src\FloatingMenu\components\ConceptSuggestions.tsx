import React, { useState, useEffect } from 'react';
import { Loader2, Check } from 'lucide-react';
import { ConceptSuggestion } from '@repo/api';
import { cn } from '@repo/ui';

interface ConceptSuggestionsProps {
    suggestions: ConceptSuggestion[];
    isLoading: boolean;
    error: string | null;
    onSelectConcepts: (selectedConcepts: ConceptSuggestion[]) => void;
    onCancel: () => void;
    sourceNodeTitle?: string;
}

export function ConceptSuggestions({
    suggestions,
    isLoading,
    error,
    onSelectConcepts,
    onCancel,
    sourceNodeTitle
}: ConceptSuggestionsProps) {
    // Initialize with an empty selection object
    const [selectedSuggestions, setSelectedSuggestions] = useState<Record<string, boolean>>({});

    // Reset selections when suggestions change
    useEffect(() => {
        console.log('[ConceptSuggestions] Suggestions changed, resetting selection state');
        console.log('[ConceptSuggestions] New suggestions:', suggestions);

        // Verify that all suggestions have valid IDs
        const allHaveIds = suggestions.every(suggestion => !!suggestion.id);
        if (!allHaveIds) {
            console.error(
                '[ConceptSuggestions] Some suggestions are missing IDs!',
                suggestions.filter(s => !s.id)
            );
        }

        // Reset selection state
        setSelectedSuggestions({});
    }, [suggestions]);

    const toggleSelection = (suggestionId: string, event: React.MouseEvent) => {
        // Stop event propagation to prevent multiple toggles
        event.stopPropagation();

        if (!suggestionId) {
            console.error(
                '[ConceptSuggestions] Cannot toggle selection for undefined suggestionId'
            );
            return;
        }

        // Make sure we're properly toggling the boolean value
        setSelectedSuggestions(prev => {
            const newState = { ...prev };
            // Explicitly set to true if it was falsy, or false if it was true
            const currentValue = prev[suggestionId] === true;
            newState[suggestionId] = !currentValue;

            // Log the change
            console.log(
                `[ConceptSuggestions] Toggling selection for concept ${suggestionId}: ${currentValue} -> ${!currentValue}`
            );

            return newState;
        });
    };

    const handleAddSelected = () => {
        // Only include suggestions where the selection state is explicitly true
        const selected = suggestions.filter(
            suggestion => selectedSuggestions[suggestion.id] === true
        );

        console.log(`[ConceptSuggestions] Adding ${selected.length} selected concepts`);
        console.log('[ConceptSuggestions] Selected concepts:', selected);

        onSelectConcepts(selected);
    };

    const selectAll = () => {
        const allSelected: Record<string, boolean> = {};

        // Only include suggestions with valid IDs
        suggestions.forEach(suggestion => {
            if (suggestion && suggestion.id) {
                allSelected[suggestion.id] = true;
            }
        });

        console.log('[ConceptSuggestions] Selected all concepts:', Object.keys(allSelected).length);
        setSelectedSuggestions(allSelected);
    };

    const deselectAll = () => {
        setSelectedSuggestions({});
    };

    // Count selected suggestions - make sure we're only counting true values
    const selectedCount = Object.entries(selectedSuggestions).filter(
        ([_, isSelected]) => isSelected === true
    ).length;

    return (
        <div className='flex flex-col gap-3'>
            {isLoading ? (
                <div className='flex flex-col items-center justify-center py-6'>
                    <Loader2 className='h-8 w-8 animate-spin text-gray-400' />
                    <p className='mt-2 text-sm text-gray-500'>Finding related concepts...</p>
                </div>
            ) : error ? (
                <div className='rounded-md bg-red-50 p-3'>
                    <p className='text-sm text-red-600'>{error}</p>
                    <button
                        className='mt-2 rounded-md bg-red-100 px-3 py-1 text-xs font-medium text-red-700 hover:bg-red-200'
                        onClick={onCancel}
                    >
                        Close
                    </button>
                </div>
            ) : suggestions.length === 0 ? (
                <div className='rounded-md bg-gray-50 p-3'>
                    <p className='text-sm text-gray-600'>No related concepts found.</p>
                    <button
                        className='mt-2 rounded-md bg-gray-100 px-3 py-1 text-xs font-medium hover:bg-gray-200'
                        onClick={onCancel}
                    >
                        Close
                    </button>
                </div>
            ) : (
                <>
                    <div className='rounded-md bg-gray-50 p-3'>
                        <p className='text-xs text-gray-600'>
                            {suggestions.length} related concept
                            {suggestions.length !== 1 ? 's' : ''} found for{' '}
                            <span className='font-medium'>{sourceNodeTitle}</span>. Select the ones
                            you want to add.
                        </p>
                    </div>

                    <div className='flex justify-between'>
                        <div className='flex gap-2'>
                            <button
                                className='rounded-md bg-gray-100 px-2 py-1 text-xs font-medium hover:bg-gray-200'
                                onClick={selectAll}
                            >
                                Select All
                            </button>
                            <button
                                className='rounded-md bg-gray-100 px-2 py-1 text-xs font-medium hover:bg-gray-200'
                                onClick={deselectAll}
                                disabled={selectedCount === 0}
                            >
                                Deselect All
                            </button>
                        </div>
                        <div className='text-xs text-gray-500'>
                            {selectedCount} of {suggestions.length} selected
                        </div>
                    </div>

                    <div className='max-h-[300px] overflow-y-auto rounded-md border border-gray-200'>
                        {suggestions.map(suggestion => (
                            <div
                                key={suggestion.id}
                                className={cn(
                                    'flex cursor-pointer items-start border-b border-gray-200 p-3 last:border-b-0 hover:bg-gray-50',
                                    selectedSuggestions[suggestion.id] === true &&
                                        'bg-blue-50 hover:bg-blue-100'
                                )}
                                onClick={event => toggleSelection(suggestion.id, event)}
                            >
                                <div
                                    className={cn(
                                        'mr-2 flex h-5 w-5 flex-shrink-0 cursor-pointer items-center justify-center rounded-md border',
                                        selectedSuggestions[suggestion.id] === true
                                            ? 'border-blue-500 bg-blue-500 text-white'
                                            : 'border-gray-300 bg-white'
                                    )}
                                    onClick={event => {
                                        // Prevent the parent's onClick from firing
                                        event.stopPropagation();

                                        // Log the current state before toggling
                                        console.log(
                                            `[ConceptSuggestions] Checkbox clicked for ${suggestion.id}`,
                                            `Current state: ${selectedSuggestions[suggestion.id]}`
                                        );

                                        toggleSelection(suggestion.id, event);
                                    }}
                                >
                                    {selectedSuggestions[suggestion.id] === true && (
                                        <Check className='h-3 w-3' />
                                    )}
                                </div>
                                <div className='flex-1'>
                                    <div className='flex items-center justify-between'>
                                        <h4 className='text-sm font-medium'>{suggestion.title}</h4>
                                        <span className='rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-600'>
                                            {suggestion.edgeType}
                                        </span>
                                    </div>
                                    <p className='mt-1 text-xs text-gray-600'>
                                        {suggestion.description}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className='flex justify-end gap-2'>
                        <button
                            className='rounded-md bg-gray-100 px-3 py-1 text-xs font-medium hover:bg-gray-200'
                            onClick={onCancel}
                        >
                            Cancel
                        </button>
                        <button
                            className={cn(
                                'rounded-md px-3 py-1 text-xs font-medium',
                                selectedCount > 0
                                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                                    : 'bg-gray-100 text-gray-400'
                            )}
                            onClick={() => {
                                console.log('[ConceptSuggestions] Add Selected button clicked');
                                console.log('[ConceptSuggestions] Selected count:', selectedCount);
                                console.log(
                                    '[ConceptSuggestions] Selected state:',
                                    selectedSuggestions
                                );
                                handleAddSelected();
                            }}
                            disabled={selectedCount === 0}
                        >
                            Add Selected ({selectedCount})
                        </button>
                    </div>
                </>
            )}
        </div>
    );
}
