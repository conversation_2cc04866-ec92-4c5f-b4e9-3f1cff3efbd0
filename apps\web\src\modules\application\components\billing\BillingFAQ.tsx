import {
    Card,
    CardHeader,
    CardContent,
    CardTitle,
    CardDescription,
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@repo/ui';

// FAQ item
interface FAQItem {
    question: string;
    answer: string;
}

// Define FAQ items
const faqItems: FAQItem[] = [
    {
        question: 'What payment methods do you accept?',
        answer: 'We accept all major credit cards (Visa, Mastercard, American Express, Discover) through our secure payment processor, Stripe.'
    },
    {
        question: 'Can I cancel my subscription at any time?',
        answer: 'Yes, you can cancel your subscription at any time. Your Pro features will remain active until the end of your current billing period.'
    },
    {
        question: 'Is there a free trial for Pro?',
        answer: "We currently don't offer a free trial, but we do have a 30-day money-back guarantee if you're not satisfied with the Pro features."
    },
    {
        question: 'What happens to my data if I downgrade from Pro to Free?',
        answer: "Your data remains intact, but you'll lose access to Pro-only features. If you have more than 5 decks, you'll still be able to access them but not create new ones until you're below the limit."
    },
    {
        question: 'Do you offer educational or team discounts?',
        answer: 'Yes, we offer special pricing for educational institutions and teams. Please contact our support team for more information.'
    }
];

export default function BillingFAQ() {
    return (
        <Card className='mt-8'>
            <CardHeader>
                <CardTitle>Frequently Asked Questions</CardTitle>
                <CardDescription>Common questions about billing and subscriptions</CardDescription>
            </CardHeader>
            <CardContent>
                <Accordion type='single' collapsible className='w-full'>
                    {faqItems.map((item, index) => (
                        <AccordionItem key={index} value={`item-${index}`}>
                            <AccordionTrigger className='text-left'>
                                {item.question}
                            </AccordionTrigger>
                            <AccordionContent>{item.answer}</AccordionContent>
                        </AccordionItem>
                    ))}
                </Accordion>
            </CardContent>
        </Card>
    );
}
