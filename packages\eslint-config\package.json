{"name": "@repo/eslint-config", "version": "0.0.0", "private": true, "type": "module", "exports": {".": "./index.js", "./react": "./react.js"}, "dependencies": {"@eslint/js": "^9.15.0", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import-x": "^4.4.3", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.12.0", "typescript-eslint": "^8.16.0"}, "publishConfig": {"access": "public"}}