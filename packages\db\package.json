{"name": "@repo/db", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./exports.ts"}, "types": "./exports.ts", "license": "MIT", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf .turbo node_modules dist"}, "dependencies": {"@journeyapps/wa-sqlite": "^1.2.2", "@supabase/supabase-js": "^2.49.4", "@triplit/cli": "^1.0.35", "@triplit/client": "^1.0.27", "@triplit/react": "^1.0.27", "@types/jsonwebtoken": "^9.0.9", "better-auth": "^1.2.7", "better-sqlite3": "^11.9.1", "jsonwebtoken": "^9.0.2", "react": "19.1.0", "uuid": "^11.1.0", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1", "zod": "^3.23.8"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "@types/uuid": "^10.0.0", "typescript": "^5.5.4"}}