{"name": "turborepo-react-router-v7-starter", "version": "0.0.1", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><<EMAIL>>", "license": "MIT", "type": "module", "scripts": {"build": "turbo build", "dev": "turbo dev", "test": "turbo test", "test:watch": "turbo test:watch", "lint": "turbo lint", "format": "turbo format", "ui": "pnpm --filter=ui shadcn", "list": "node scripts/dev.js list"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "eslint": "^9.16.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.4.1", "stylelint": "^16.11.0", "turbo": "^2.5.0", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "engines": {"node": ">=20.10.0"}, "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@hookform/resolvers": "^5.0.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@triplit/cli": "^1.0.35", "@triplit/client": "^1.0.27", "@triplit/react": "^1.0.27", "@types/lodash": "^4.17.17", "boxen": "^8.0.1", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "katex": "^0.16.10", "lodash": "^4.17.21", "lru-cache": "^10.2.0", "openai": "^4.89.0", "ora": "^8.2.0", "pdfjs-dist": "^5.1.91", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-markdown": "^10.0.0", "react-router-dom": "7.5.1", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "tiptap-markdown": "^0.8.10", "uuid": "^11.1.0", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1", "zod": "^3.24.2", "zustand": "^5.0.3"}}