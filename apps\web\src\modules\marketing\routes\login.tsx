import { useEffect, useState } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@repo/db';
import {
    Button,
    Input,
    Card,
    CardHeader,
    CardContent,
    CardFooter,
    Label,
    Form,
    FormField,
    FormItem,
    FormControl,
    FormMessage
} from '@repo/ui';

// Schema for login form
const loginSchema = z.object({
    email: z.string().email({ message: 'Invalid email address' }),
    password: z.string().min(6, { message: 'Password must be at least 6 characters' })
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function Login() {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [signOutMessage, setSignOutMessage] = useState<string | null>(null);
    const { signIn } = useAuth();

    // Initialize form with validation
    const form = useForm<LoginFormValues>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: ''
        }
    });

    // Check for signout message in URL
    useEffect(() => {
        const signedOut = searchParams.get('signedOut');
        if (signedOut === 'true') {
            setSignOutMessage('You have been signed out successfully');
        }
    }, [searchParams]);

    // Form submission handler
    const onSubmit = async (values: LoginFormValues) => {
        setLoading(true);
        setError(null);

        try {
            const { success, error: signInError } = await signIn(values.email, values.password);

            if (!success) {
                setError(signInError || 'Failed to sign in');
                setLoading(false);
                return;
            }

            // Successful login - navigate to dashboard
            console.log('[LOGIN] Sign in successful');
            navigate('/app/dashboard');
        } catch (err) {
            console.error('[LOGIN] Error during sign in:', err);
            setError('An unexpected error occurred. Please try again.');
            setLoading(false);
        }
    };

    return (
        <div className='flex min-h-screen items-center justify-center'>
            <Card className='w-full max-w-md p-6'>
                <CardHeader>
                    <h1 className='text-2xl font-semibold'>Login</h1>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
                            <FormField
                                control={form.control}
                                name='email'
                                render={({ field }) => (
                                    <FormItem>
                                        <Label>Email</Label>
                                        <FormControl>
                                            <Input
                                                type='email'
                                                placeholder='<EMAIL>'
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name='password'
                                render={({ field }) => (
                                    <FormItem>
                                        <Label>Password</Label>
                                        <FormControl>
                                            <Input
                                                type='password'
                                                placeholder='********'
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            {error && <p className='text-sm text-red-500'>{error}</p>}
                            {signOutMessage && (
                                <p className='text-sm text-green-500'>{signOutMessage}</p>
                            )}
                            {/* Disable button while loading */}
                            <Button type='submit' disabled={loading} className='w-full'>
                                {loading ? 'Logging in...' : 'Login'}
                            </Button>
                        </form>
                    </Form>
                </CardContent>
                <CardFooter className='flex justify-between'>
                    <p className='text-sm'>
                        Don't have an account?{' '}
                        <Link to='/register' className='underline'>
                            Register
                        </Link>
                    </p>
                </CardFooter>
            </Card>
        </div>
    );
}
