import { Tb<PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, Tb<PERSON>hartB<PERSON>, TbDashboardFilled, TbBox } from 'react-icons/tb';
import { Button, cn } from '../../export';
import { Link, useLocation } from 'react-router';

const isActive = (path: string, currentPath: string) => {
    return path === '/dashboard' ? currentPath === path : currentPath.startsWith(path);
};

export function SidebarNavigationOrganism() {
    const location = useLocation();
    const currentPath = location.pathname;

    const navItems = [
        { href: '/app/dashboard', icon: TbDashboardFilled, label: 'Dashboard' },
        { href: '/app/cards', icon: TbBox, label: 'Cards' },
        { href: '/app/community', icon: TbUsers, label: 'Community' },
        { href: '/app/analytics', icon: TbChartBar, label: 'Analytics' },
        { href: '/blog/updates', icon: Tb<PERSON><PERSON>B<PERSON>, label: 'Updates' }
    ];

    return (
        <nav className='grid items-start gap-1 py-1'>
            <h3 className='mb-1 px-3 text-[11px] font-semibold uppercase tracking-wider text-gray-500'>
                Navigation
            </h3>
            {navItems.map(item => {
                const active = isActive(item.href, currentPath);
                return (
                    <Button
                        key={item.href}
                        variant='ghost'
                        className={cn(
                            'h-7 w-full justify-start rounded-md px-3 text-[13px] font-medium text-gray-600 transition-colors duration-150 hover:bg-gray-50 hover:text-gray-900',
                            active && 'bg-gray-50 text-gray-900'
                        )}
                        asChild
                    >
                        <Link to={item.href}>
                            <item.icon
                                className={cn(
                                    'mr-2 h-4 w-4',
                                    active
                                        ? 'text-indigo-600'
                                        : 'text-gray-400 group-hover:text-gray-500'
                                )}
                            />
                            <span>{item.label}</span>
                        </Link>
                    </Button>
                );
            })}
        </nav>
    );
}
