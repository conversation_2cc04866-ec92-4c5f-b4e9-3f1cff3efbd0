import { Node } from '@xyflow/react';

// Calculates the bounding box for a given set of nodes with padding.
export const getBoundingBox = (nodesToBound: Node[]) => {
  if (!nodesToBound || nodesToBound.length === 0) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0, width: 0, height: 0 };
  }
  const BORDER_PADDING = 60; // Increased padding around the bounded nodes

  let minX = Infinity,
    minY = Infinity,
    maxX = -Infinity,
    maxY = -Infinity;

  nodesToBound.forEach(node => {
    const x = node.position.x;
    const y = node.position.y;
    // Use node dimensions if available, otherwise use MapNode default width/height
    // MapNode has a fixed width of 384px and min-height of 250px
    const width = node.width || 384;
    const height = node.height || 250;

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });

  // Apply padding
  minX -= BORDER_PADDING;
  minY -= BORDER_PADDING;
  maxX += BORDER_PADDING;
  maxY += BORDER_PADDING;

  const width = maxX - minX;
  const height = maxY - minY;

  return { minX, minY, maxX, maxY, width, height };
};
