{"name": "@repo/utils", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./src/index.ts", "./zoom-levels": "./src/zoom-levels.ts", "./group-types": "./src/group-types.ts", "./edge-types": "./src/edge-types.ts", "./learning-status": "./src/learning-status.ts", "./node-sections": "./src/node-sections.ts"}, "types": "./src/index.ts", "license": "MIT", "scripts": {"lint": "eslint . --max-warnings 0", "clean": "rm -rf .turbo node_modules dist"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "typescript": "^5.5.4"}}