import React, { useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';
import { Button } from '@repo/ui';

interface TiptapMarkdownEditorProps {
    initialValue?: string;
    onSave?: (value: string) => void;
    onCancel?: () => void;
    placeholder?: string;
}

export function TiptapMarkdownEditor({
    initialValue = '',
    onSave,
    onCancel,
    placeholder = 'Enter section content...'
}: TiptapMarkdownEditorProps) {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                // Disable some extensions that might cause width issues
                codeBlock: {
                    HTMLAttributes: {
                        class: 'code-block-wrapper'
                    }
                }
            }),
            Markdown.configure({
                html: false,
                transformCopiedText: true,
                transformPastedText: true
            })
        ],
        content: initialValue,
        editorProps: {
            attributes: {
                class: 'tiptap-editor prose prose-sm max-w-none focus:outline-none',
                'data-placeholder': placeholder
            }
        },
        immediatelyRender: false
    });

    // Focus editor on mount
    useEffect(() => {
        if (editor) {
            editor.commands.focus();
        }
    }, [editor]);

    // Handle save
    const handleSave = () => {
        if (editor && onSave) {
            const markdownContent = editor.storage.markdown.getMarkdown();
            onSave(markdownContent);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        if (onCancel) {
            onCancel();
        }
    };

    // Handle keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                handleCancel();
            } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                handleSave();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, []);

    if (!editor) {
        return null;
    }

    return (
        <div className="tiptap-markdown-editor">
            <div className="min-h-[100px] w-full rounded-md border border-gray-200 bg-white p-2 text-sm text-gray-800 focus-within:ring-1 focus-within:ring-blue-400">
                <EditorContent 
                    editor={editor} 
                    className="w-full max-w-full overflow-hidden"
                />
            </div>
            <div className="mt-2 flex justify-end gap-2">
                <Button variant="outline" size="sm" onClick={handleCancel} className="text-xs">
                    Cancel
                </Button>
                <Button variant="default" size="sm" onClick={handleSave} className="text-xs">
                    Save
                </Button>
            </div>
        </div>
    );
}
