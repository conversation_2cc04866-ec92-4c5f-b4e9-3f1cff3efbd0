import React, { useEffect, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';

interface TiptapMarkdownEditorProps {
    initialValue?: string;
    onSave?: (value: string) => void;
    onCancel?: () => void;
    placeholder?: string;
}

export function TiptapMarkdownEditor({
    initialValue = '',
    onSave,
    onCancel,
    placeholder = 'Enter section content...'
}: TiptapMarkdownEditorProps) {
    console.log('[SectionClickBug] TiptapMarkdownEditor rendered with initialValue:', initialValue);

    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                // Disable some extensions that might cause width issues
                codeBlock: {
                    HTMLAttributes: {
                        class: 'code-block-wrapper'
                    }
                }
            }),
            Markdown.configure({
                html: false,
                transformCopiedText: true,
                transformPastedText: true
            })
        ],
        content: initialValue,
        editorProps: {
            attributes: {
                class: 'markdown-content prose prose-sm max-w-none focus:outline-none',
                'data-placeholder': placeholder
            }
        },
        immediatelyRender: false,
        // Auto-save on content change
        onUpdate: ({ editor }) => {
            console.log('[SectionClickBug] Editor content updated');
            if (onSave) {
                // Debounce auto-save to avoid too frequent saves
                setTimeout(() => {
                    const markdownContent = editor.storage.markdown.getMarkdown();
                    console.log('[SectionClickBug] Auto-saving content:', markdownContent);
                    onSave(markdownContent);
                }, 500);
            }
        },
        onBlur: ({ editor }) => {
            console.log('[SectionClickBug] Editor lost focus, auto-saving');
            if (onSave) {
                const markdownContent = editor.storage.markdown.getMarkdown();
                console.log('[SectionClickBug] Saving content on blur:', markdownContent);
                onSave(markdownContent);
            }
        }
    });

    // Focus editor on mount and set cursor position
    useEffect(() => {
        if (editor) {
            console.log('[SectionClickBug] Editor ready, focusing');
            // Focus at the end of content
            editor.commands.focus('end');
        }
    }, [editor]);

    // Handle keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                console.log('[SectionClickBug] Escape pressed, canceling edit');
                if (onCancel) {
                    onCancel();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [onCancel]);

    if (!editor) {
        console.log('[SectionClickBug] Editor not ready yet');
        return null;
    }

    return (
        <div
            className='tiptap-markdown-editor w-full max-w-full overflow-hidden'
            onClick={e => {
                console.log('[SectionClickBug] Editor container clicked');
                e.stopPropagation();
            }}
        >
            <EditorContent
                editor={editor}
                className='w-full max-w-full cursor-text overflow-hidden break-words'
                onClick={e => {
                    console.log('[SectionClickBug] EditorContent clicked');
                    e.stopPropagation();
                }}
            />
        </div>
    );
}
