import React, { useEffect, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';

interface TiptapMarkdownEditorProps {
    initialValue?: string;
    onSave?: (value: string) => void;
    onCancel?: () => void;
    placeholder?: string;
    clickPosition?: { x: number; y: number };
}

export function TiptapMarkdownEditor({
    initialValue = '',
    onSave,
    onCancel,
    placeholder = 'Enter section content...',
    clickPosition
}: TiptapMarkdownEditorProps) {
    console.log('[SectionClickBug] TiptapMarkdownEditor rendered with initialValue:', initialValue);

    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                // Disable some extensions that might cause width issues
                codeBlock: {
                    HTMLAttributes: {
                        class: 'code-block-wrapper'
                    }
                }
            }),
            Markdown.configure({
                html: false,
                transformCopiedText: true,
                transformPastedText: true
            })
        ],
        content: initialValue,
        editorProps: {
            attributes: {
                class: 'markdown-content prose prose-sm max-w-none focus:outline-none',
                'data-placeholder': placeholder
            }
        },
        immediatelyRender: false,
        // Auto-save on content change
        onUpdate: ({ editor }) => {
            console.log('[SectionClickBug] Editor content updated');
            if (onSave) {
                // Debounce auto-save to avoid too frequent saves
                setTimeout(() => {
                    const markdownContent = editor.storage.markdown.getMarkdown();
                    console.log('[SectionClickBug] Auto-saving content:', markdownContent);
                    onSave(markdownContent);
                }, 500);
            }
        },
        onBlur: ({ editor }) => {
            console.log('[SectionClickBug] Editor lost focus, auto-saving');
            if (onSave) {
                const markdownContent = editor.storage.markdown.getMarkdown();
                console.log('[SectionClickBug] Saving content on blur:', markdownContent);
                onSave(markdownContent);
            }
        }
    });

    // Focus editor on mount and set cursor position
    useEffect(() => {
        if (editor) {
            console.log('[SectionClickBug] Editor ready, focusing');
            console.log('[SectionClickBug] Click position received:', clickPosition);

            if (clickPosition) {
                // Use setTimeout to ensure the editor is fully rendered
                setTimeout(() => {
                    try {
                        // Get the editor DOM element
                        const editorElement = editor.view.dom;
                        console.log('[SectionClickBug] Editor DOM element:', editorElement);

                        // Get the position in the document from the click coordinates
                        const pos = editor.view.posAtCoords({
                            left: clickPosition.x,
                            top: clickPosition.y
                        });

                        console.log('[SectionClickBug] Calculated position:', pos);

                        if (pos) {
                            // Set cursor at the clicked position
                            editor.commands.focus();
                            editor.commands.setTextSelection(pos.pos);
                            console.log('[SectionClickBug] Cursor set at position:', pos.pos);
                        } else {
                            // Fallback to end if position calculation fails
                            console.log(
                                '[SectionClickBug] Position calculation failed, focusing at end'
                            );
                            editor.commands.focus('end');
                        }
                    } catch (error) {
                        console.error('[SectionClickBug] Error setting cursor position:', error);
                        // Fallback to end if there's an error
                        editor.commands.focus('end');
                    }
                }, 50);
            } else {
                // No click position provided, focus at end
                editor.commands.focus('end');
            }
        }
    }, [editor, clickPosition]);

    // Handle keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                console.log('[SectionClickBug] Escape pressed, canceling edit');
                if (onCancel) {
                    onCancel();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [onCancel]);

    if (!editor) {
        console.log('[SectionClickBug] Editor not ready yet');
        return null;
    }

    return (
        <div
            className='tiptap-markdown-editor w-full max-w-full overflow-hidden'
            onClick={e => {
                console.log('[SectionClickBug] Editor container clicked');
                e.stopPropagation();
            }}
        >
            <EditorContent
                editor={editor}
                className='w-full max-w-full cursor-text overflow-hidden break-words'
                onClick={e => {
                    console.log('[SectionClickBug] EditorContent clicked');
                    e.stopPropagation();
                }}
            />
        </div>
    );
}
