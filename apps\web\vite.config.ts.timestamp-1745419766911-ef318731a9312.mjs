// vite.config.ts
import sharedConfig from "file:///C:/Users/<USER>/unisono2/packages/vite-config/index.js";
import path from "path";
import { defineConfig, mergeConfig, loadEnv } from "file:///C:/Users/<USER>/unisono2/node_modules/.pnpm/vite@5.4.18_@types+node@20.17.30_sass@1.87.0_terser@5.39.0/node_modules/vite/dist/node/index.js";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\unisono2\\apps\\web";
var vite_config_default = defineConfig((configEnv) => {
  const env = loadEnv(configEnv.mode, process.cwd(), "");
  return mergeConfig(
    sharedConfig({
      env: configEnv,
      lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
      ssrInput: "./server/app.ts",
      testSetupFiles: "./src/setupTest.ts"
    }),
    defineConfig({
      // Define environment variables to be replaced in the browser
      define: {
        // Expose OpenAI API key to the browser
        "import.meta.env.OPENAI_API_KEY": JSON.stringify(env.OPENAI_API_KEY),
        // Also expose with VITE_ prefix for consistency
        "import.meta.env.VITE_OPENAI_API_KEY": JSON.stringify(
          env.VITE_OPENAI_API_KEY || env.OPENAI_API_KEY
        )
      },
      resolve: {
        alias: {
          "@": path.resolve(__vite_injected_original_dirname, "./src")
        }
      }
    }),
    false
  );
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
