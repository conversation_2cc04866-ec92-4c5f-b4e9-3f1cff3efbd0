import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuContent
} from '@repo/ui';
import { Link } from 'react-router';
import { TbChevronDown, TbCreditCard, TbSettings, TbHelpCircle, TbLogout } from 'react-icons/tb';

export function SidebarUserOrganism() {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <button className='flex w-full items-center justify-between gap-2 rounded-md p-1.5 transition-colors duration-150 hover:bg-gray-50'>
                    <div className='flex items-center gap-2'>
                        <div className='flex h-7 w-7 items-center justify-center rounded-full bg-gradient-to-br from-indigo-50 to-blue-100 text-xs font-semibold text-indigo-700 shadow-[0_0_0_1px_rgba(79,70,229,0.1)]'>
                            {'U'}
                        </div>
                        <div className='flex flex-col'>
                            <span className='text-sm font-medium text-gray-700'>{'User'}</span>
                        </div>
                    </div>
                    <TbChevronDown className='h-4 w-4 text-gray-400' />
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='start' className='w-[200px]'>
                <Link to='/app/billing' className='w-full'>
                    <DropdownMenuItem className='cursor-pointer text-xs'>
                        <TbCreditCard className='mr-2 h-4 w-4 text-gray-500' />
                        Billing
                    </DropdownMenuItem>
                </Link>
                <Link to='/app/account' className='w-full'>
                    <DropdownMenuItem className='cursor-pointer text-xs'>
                        <TbSettings className='mr-2 h-4 w-4 text-gray-500' />
                        Account Settings
                    </DropdownMenuItem>
                </Link>
                <DropdownMenuSeparator />
                <DropdownMenuItem className='cursor-pointer text-xs'>
                    <TbHelpCircle className='mr-2 h-4 w-4 text-gray-500' />
                    Help & Support
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    className='cursor-pointer text-xs text-red-600 focus:text-red-600'
                    onClick={() => {
                        // Sign out using Supabase
                        if (typeof window !== 'undefined') {
                            // This will redirect to the login page after sign out
                            window.location.href = '/login?signout=true';
                        }
                    }}
                >
                    <TbLogout className='mr-2 h-4 w-4 text-red-500' />
                    Sign Out
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
