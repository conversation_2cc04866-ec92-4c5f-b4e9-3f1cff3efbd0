import { useMapZoom } from './useMapZoom';
import { ZOOM_LEVELS } from '@repo/utils';
import { useEffect, useState } from 'react';
import { useReactFlow } from '@xyflow/react';

export function MapZoom() {
    const { zoom } = useMapZoom();
    const [currentZoom, setCurrentZoom] = useState(zoom);
    const reactFlowInstance = useReactFlow();

    // Format zoom level as percentage
    const zoomPercentage = Math.round(currentZoom * 100);

    // Determine which predefined level we're at
    const getCurrentZoomLevelName = () => {
        const entries = Object.entries(ZOOM_LEVELS);
        for (const [name, value] of entries) {
            if (Math.abs(value - currentZoom) < 0.01) {
                return name.toLowerCase();
            }
        }
        return '';
    };

    const zoomLevelName = getCurrentZoomLevelName();

    // Update zoom level when viewport changes
    useEffect(() => {
        // Use a ref to track if we're in an update cycle to prevent infinite loops
        let isUpdating = false;

        const updateZoom = () => {
            if (isUpdating) return;

            isUpdating = true;
            const { zoom: newZoom } = reactFlowInstance.getViewport();

            // Only update if the zoom has actually changed
            if (Math.abs(newZoom - currentZoom) > 0.001) {
                setCurrentZoom(newZoom);
            }

            isUpdating = false;
        };

        // Create a MutationObserver to watch for viewport changes
        const observer = new MutationObserver(updateZoom);

        // Get the ReactFlow container
        const container = document.querySelector('.react-flow');
        if (container) {
            // Observe changes to the container's style attribute (which includes transform)
            observer.observe(container, { attributes: true, attributeFilter: ['style'] });

            // Initial update
            updateZoom();
        }

        // Clean up observer on unmount
        return () => {
            observer.disconnect();
        };
    }, [reactFlowInstance, currentZoom]);

    return (
        <div className='rounded-md bg-white/80 px-3 py-2 shadow-sm backdrop-blur-sm'>
            <div className='text-center text-sm font-medium'>
                {zoomPercentage}%
                {zoomLevelName && (
                    <span className='text-muted-foreground ml-1 text-xs'>({zoomLevelName})</span>
                )}
            </div>
        </div>
    );
}
