import { useCallback } from 'react';
import {
    useReact<PERSON>low,
    OnNodesChange,
    OnEdgesChange,
    OnConnect,
    NodeChange,
    EdgeChange,
    Connection,
    Node,
    Edge,
    applyNodeChanges,
    applyEdgeChanges,
    addEdge,
    MarkerType
} from '@xyflow/react';
import { EDGE_TYPES } from '@repo/utils';

interface UseMapInteractionHandlingProps {
    setNodes: ReturnType<typeof useReactFlow>['setNodes'];
    setEdges: ReturnType<typeof useReactFlow>['setEdges'];
    getNodes: ReturnType<typeof useReactFlow>['getNodes'];
    debouncedUpdateMap: () => void;
    addChangedNodeId: (nodeId: string | string[]) => void;
}

export const useMapInteractionHandling = ({
    setNodes,
    setEdges,
    getNodes,
    debouncedUpdateMap,
    addChangedNodeId
}: UseMapInteractionHandlingProps) => {
    const reactFlowInstance = useReactFlow();

    const onNodesChange: OnNodesChange = useCallback(
        (changes: NodeChange[]) => {
            // Apply changes directly - ReactFlow handles parent-child relationships internally
            setNodes((nds: Node[]) => applyNodeChanges(changes, nds));
            debouncedUpdateMap();
        },
        [setNodes, debouncedUpdateMap]
    );

    const onEdgesChange: OnEdgesChange = useCallback(
        (changes: EdgeChange[]) => {
            setEdges((eds: Edge[]) => applyEdgeChanges(changes, eds));
            debouncedUpdateMap();
        },
        [setEdges, debouncedUpdateMap]
    );

    const onConnect: OnConnect = useCallback(
        (connection: Connection) => {
            console.log(
                '[hooks/useMapInteractionHandling.ts:onConnect] Creating new edge:',
                connection
            );

            // Get the source and target nodes to determine the best handles if not specified
            const sourceNode = reactFlowInstance.getNode(connection.source);
            const targetNode = reactFlowInstance.getNode(connection.target);

            // Create a modified connection with sourceHandle and targetHandle if they're missing
            const modifiedConnection = { ...connection };

            // If sourceHandle is missing, determine the best handle based on node positions
            if (!modifiedConnection.sourceHandle && sourceNode && targetNode) {
                console.log(
                    '[hooks/useMapInteractionHandling.ts:onConnect] Source handle missing, determining best handle'
                );

                // Calculate the angle between nodes to determine the best handle
                const sourceX = sourceNode.position.x + (sourceNode.width || 0) / 2;
                const sourceY = sourceNode.position.y + (sourceNode.height || 0) / 2;
                const targetX = targetNode.position.x + (targetNode.width || 0) / 2;
                const targetY = targetNode.position.y + (targetNode.height || 0) / 2;

                const dx = targetX - sourceX;
                const dy = targetY - sourceY;
                const angle = Math.atan2(dy, dx) * (180 / Math.PI);

                console.log(`[useMapInteractionHandling] Angle between nodes: ${angle} degrees`);

                // Determine the best handle based on the angle
                if (angle >= -45 && angle < 45) {
                    modifiedConnection.sourceHandle = 'right-source'; // Right side
                } else if (angle >= 45 && angle < 135) {
                    modifiedConnection.sourceHandle = 'bottom-source'; // Bottom side
                } else if (angle >= 135 || angle < -135) {
                    modifiedConnection.sourceHandle = 'left-source'; // Left side
                } else {
                    modifiedConnection.sourceHandle = 'top-source'; // Top side
                }

                console.log(
                    `[hooks/useMapInteractionHandling.ts:onConnect] Selected source handle: ${modifiedConnection.sourceHandle}`
                );
            }

            // If targetHandle is missing, determine the best handle based on node positions
            if (!modifiedConnection.targetHandle && sourceNode && targetNode) {
                console.log(
                    '[hooks/useMapInteractionHandling.ts:onConnect] Target handle missing, determining best handle'
                );

                // Calculate the angle between nodes to determine the best handle
                const sourceX = sourceNode.position.x + (sourceNode.width || 0) / 2;
                const sourceY = sourceNode.position.y + (sourceNode.height || 0) / 2;
                const targetX = targetNode.position.x + (targetNode.width || 0) / 2;
                const targetY = targetNode.position.y + (targetNode.height || 0) / 2;

                const dx = sourceX - targetX;
                const dy = sourceY - targetY;
                const angle = Math.atan2(dy, dx) * (180 / Math.PI);

                console.log(
                    `[useMapInteractionHandling] Target angle between nodes: ${angle} degrees`
                );

                // Determine the best handle based on the angle
                if (angle >= -45 && angle < 45) {
                    modifiedConnection.targetHandle = 'right-target'; // Right side
                } else if (angle >= 45 && angle < 135) {
                    modifiedConnection.targetHandle = 'bottom-target'; // Bottom side
                } else if (angle >= 135 || angle < -135) {
                    modifiedConnection.targetHandle = 'left-target'; // Left side
                } else {
                    modifiedConnection.targetHandle = 'top-target'; // Top side
                }

                console.log(
                    `[hooks/useMapInteractionHandling.ts:onConnect] Selected target handle: ${modifiedConnection.targetHandle}`
                );
            }

            // Get the first edge type from MapConstants as default
            const defaultEdgeType = Object.keys(EDGE_TYPES)[0] || 'instanceOf';

            const newEdge: Edge = {
                id: `edge-${modifiedConnection.source}-${modifiedConnection.target}-${modifiedConnection.sourceHandle}-${modifiedConnection.targetHandle}`,
                ...modifiedConnection,
                // Initialize edge data with the default type
                data: {
                    edgeType: defaultEdgeType,
                    sourceToTargetTitle:
                        EDGE_TYPES[defaultEdgeType as keyof typeof EDGE_TYPES]?.title ||
                        defaultEdgeType,
                    targetToSourceTitle:
                        EDGE_TYPES[defaultEdgeType as keyof typeof EDGE_TYPES]?.reversedTitle ||
                        `reversed ${defaultEdgeType}`
                },
                type: defaultEdgeType // Match the type in edgeTypes
            };

            setEdges((eds: Edge[]) => addEdge(newEdge, eds));
            debouncedUpdateMap();
        },
        [reactFlowInstance, setEdges, debouncedUpdateMap]
    );

    const onPaneDoubleClick = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            if (
                !(event.target instanceof Element) ||
                !event.target.classList.contains('react-flow__pane')
            )
                return;

            const position = reactFlowInstance.screenToFlowPosition({
                x: event.clientX,
                y: event.clientY
            });
            const newNodeId = crypto.randomUUID();
            const newNode: Node = {
                id: newNodeId,
                type: 'note', // Default node type
                position,
                data: {
                    conceptTitle: 'New Concept',
                    progress: 0,
                    cardCount: 0,
                    learningStatus: 'notStarted',
                    tags: [],
                    sections: {}
                }
            };
            console.log(
                '[hooks/useMapInteractionHandling.ts:onPaneDoubleClick] Adding new node:',
                newNode
            );
            setNodes((nds: Node[]) => [...nds, newNode]);
            addChangedNodeId(newNodeId); // Notify floating menu
            debouncedUpdateMap();
        },
        [reactFlowInstance, setNodes, debouncedUpdateMap, addChangedNodeId]
    );

    const onNodeDragStop = useCallback(
        (event: React.MouseEvent, draggedNode: Node) => {
            // Don't do anything special for group nodes - let ReactFlow handle it
            if (
                draggedNode.type === 'topicGroup' ||
                draggedNode.type === 'subjectGroup' ||
                draggedNode.type === 'group'
            ) {
                // Just update the map
                debouncedUpdateMap();
                return;
            }

            // Get the ReactFlow instance to use its utility functions
            const reactFlowInstance = useReactFlow();

            console.log(
                `[useMapInteractionHandling] Node drag stopped for node ${draggedNode.id} at position:`,
                draggedNode.position
            );

            // Use XYFlow's built-in getIntersectingNodes function to detect intersections
            // This is more reliable than custom intersection detection
            const intersectingGroups = reactFlowInstance.getIntersectingNodes(draggedNode, true);

            // Filter to include only group nodes
            const filteredGroups = intersectingGroups.filter(
                n => n.type === 'group' || n.type === 'topicGroup' || n.type === 'subjectGroup'
            );

            console.log(
                `[useMapInteractionHandling] Found ${filteredGroups.length} intersecting groups for node ${draggedNode.id}`
            );

            // Set parent to the first intersecting group (if any)
            let parentGroupId: string | undefined = undefined;

            if (filteredGroups.length > 0) {
                const groupNode = filteredGroups[0];
                console.log(
                    `[useMapInteractionHandling] Node ${draggedNode.id} intersects with group ${groupNode.id} - setting as parent`
                );
                parentGroupId = groupNode.id;
            }

            // Update parentId if it changed
            if (draggedNode.parentId !== parentGroupId) {
                console.log(
                    `[useMapInteractionHandling] Updating parent for node ${draggedNode.id} from ${draggedNode.parentId} to ${parentGroupId}`
                );

                setNodes((nds: Node[]) => {
                    // First, handle the node's parent relationship
                    const updatedNodes = nds.map(n => {
                        if (n.id === draggedNode.id) {
                            // If we're adding the node to a parent
                            if (parentGroupId) {
                                const parentNode = nds.find(node => node.id === parentGroupId);
                                if (parentNode && parentNode.position) {
                                    console.log(
                                        `[useMapInteractionHandling] Parent node position:`,
                                        parentNode.position
                                    );
                                    console.log(
                                        `[useMapInteractionHandling] Dragged node absolute position:`,
                                        draggedNode.position
                                    );

                                    // Calculate position relative to parent
                                    const relativePosition = {
                                        x: draggedNode.position.x - parentNode.position.x,
                                        y: draggedNode.position.y - parentNode.position.y
                                    };

                                    console.log(
                                        `[useMapInteractionHandling] Calculated relative position:`,
                                        relativePosition
                                    );

                                    return {
                                        ...n,
                                        parentId: parentGroupId,
                                        extent: 'parent',
                                        position: relativePosition,
                                        // Set zIndex to ensure the node is above the group
                                        zIndex: 5
                                    };
                                }
                            }
                            // If we're removing from a parent or parent not found
                            return { ...n, parentId: parentGroupId };
                        }
                        return n;
                    });

                    // Now, if we added the node to a group, resize the group if needed
                    if (parentGroupId) {
                        const parentNode = updatedNodes.find(node => node.id === parentGroupId);
                        const childNode = updatedNodes.find(node => node.id === draggedNode.id);

                        if (parentNode && childNode) {
                            // Get the current dimensions of the group
                            const groupWidth = parentNode.width ?? 300;
                            const groupHeight = parentNode.height ?? 300;

                            console.log(
                                `[useMapInteractionHandling] Current group dimensions: ${groupWidth}x${groupHeight}`
                            );

                            // Calculate the node's boundaries relative to the group
                            // Use 440 and 250 as default values for ConceptNode dimensions if not specified
                            const nodeWidth = draggedNode.width || 440;
                            const nodeHeight = draggedNode.height || 250;

                            // Calculate the right and bottom edges of the node relative to the group
                            const relativeRight = childNode.position.x + nodeWidth;
                            const relativeBottom = childNode.position.y + nodeHeight;

                            console.log(
                                `[useMapInteractionHandling] Node dimensions: ${nodeWidth}x${nodeHeight}`
                            );

                            console.log(
                                `[useMapInteractionHandling] Node boundaries relative to group: right=${relativeRight}, bottom=${relativeBottom}`
                            );

                            // Determine if the group needs to be resized
                            let newWidth = groupWidth;
                            let newHeight = groupHeight;

                            // Add padding
                            const padding = 20;

                            // Check if node extends beyond the right edge of the group
                            if (relativeRight + padding > groupWidth) {
                                newWidth = relativeRight + padding;
                                console.log(
                                    `[useMapInteractionHandling] Resizing group width to ${newWidth}`
                                );
                            }

                            // Check if node extends beyond the bottom edge of the group
                            if (relativeBottom + padding > groupHeight) {
                                newHeight = relativeBottom + padding;
                                console.log(
                                    `[useMapInteractionHandling] Resizing group height to ${newHeight}`
                                );
                            }

                            // Check if node extends beyond the left edge of the group (negative position)
                            if (childNode.position.x < 0) {
                                // Adjust group position and width to accommodate the node
                                const adjustX = Math.abs(childNode.position.x) + padding;
                                console.log(
                                    `[useMapInteractionHandling] Node extends ${adjustX}px beyond left edge of group`
                                );

                                // Update all child nodes' positions to account for the group's position change
                                updatedNodes = updatedNodes.map(n => {
                                    if (n.parentId === parentGroupId) {
                                        return {
                                            ...n,
                                            position: {
                                                x: n.position.x + adjustX,
                                                y: n.position.y
                                            }
                                        };
                                    }
                                    return n;
                                });

                                // Adjust group width and position
                                newWidth = newWidth + adjustX;

                                return updatedNodes.map(n => {
                                    if (n.id === parentGroupId) {
                                        console.log(
                                            `[useMapInteractionHandling] Adjusting group position and width: x-=${adjustX}, width=${newWidth}`
                                        );
                                        return {
                                            ...n,
                                            position: {
                                                x: n.position.x - adjustX,
                                                y: n.position.y
                                            },
                                            style: {
                                                ...n.style,
                                                width: newWidth,
                                                height: newHeight
                                            }
                                        };
                                    }
                                    return n;
                                });
                            }

                            // Check if node extends beyond the top edge of the group (negative position)
                            if (childNode.position.y < 0) {
                                // Adjust group position and height to accommodate the node
                                const adjustY = Math.abs(childNode.position.y) + padding;
                                console.log(
                                    `[useMapInteractionHandling] Node extends ${adjustY}px beyond top edge of group`
                                );

                                // Update all child nodes' positions to account for the group's position change
                                updatedNodes = updatedNodes.map(n => {
                                    if (n.parentId === parentGroupId) {
                                        return {
                                            ...n,
                                            position: {
                                                x: n.position.x,
                                                y: n.position.y + adjustY
                                            }
                                        };
                                    }
                                    return n;
                                });

                                // Adjust group height and position
                                newHeight = newHeight + adjustY;

                                return updatedNodes.map(n => {
                                    if (n.id === parentGroupId) {
                                        console.log(
                                            `[useMapInteractionHandling] Adjusting group position and height: y-=${adjustY}, height=${newHeight}`
                                        );
                                        return {
                                            ...n,
                                            position: {
                                                x: n.position.x,
                                                y: n.position.y - adjustY
                                            },
                                            style: {
                                                ...n.style,
                                                width: newWidth,
                                                height: newHeight
                                            }
                                        };
                                    }
                                    return n;
                                });
                            }

                            // Update the group's dimensions if they changed
                            if (newWidth !== groupWidth || newHeight !== groupHeight) {
                                return updatedNodes.map(n => {
                                    if (n.id === parentGroupId) {
                                        console.log(
                                            `[useMapInteractionHandling] Updating group ${parentGroupId} dimensions to ${newWidth}x${newHeight}`
                                        );
                                        return {
                                            ...n,
                                            style: {
                                                ...n.style,
                                                width: newWidth,
                                                height: newHeight
                                            }
                                        };
                                    }
                                    return n;
                                });
                            }
                        }
                    }

                    return updatedNodes;
                });

                debouncedUpdateMap();
            } else {
                // Even if parent didn't change, update the map
                debouncedUpdateMap();
            }
        },
        [getNodes, setNodes, debouncedUpdateMap /*, addChangedNodeId*/]
    );

    return {
        onNodesChange,
        onEdgesChange,
        onConnect,
        onPaneDoubleClick,
        onNodeDragStop
    };
};
