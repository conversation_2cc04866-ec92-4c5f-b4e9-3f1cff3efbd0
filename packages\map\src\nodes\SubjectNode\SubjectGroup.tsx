import React, { memo } from 'react';
import { Handle, Position, NodeResizer, NodeProps, useReactFlow } from '@xyflow/react';
import { GROUP_TYPES } from '@repo/utils';

export interface SubjectGroupData {
    label: string;
}

const SubjectGroup = ({ id, data, selected }: NodeProps) => {
    const { setNodes } = useReactFlow();

    // Function to handle label change
    const onLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setNodes(nodes =>
            nodes.map(node => {
                if (node.id === id) {
                    return {
                        ...node,
                        data: {
                            ...node.data,
                            label: e.target.value
                        }
                    };
                }
                return node;
            })
        );
    };

    return (
        <>
            {/* Add NodeResizer for resizability */}
            <NodeResizer
                minWidth={200}
                minHeight={200}
                isVisible={selected}
                lineClassName='border-gray-300'
                handleClassName='bg-white border-gray-300'
            />

            {/* Group container */}
            <div
                className='h-full w-full rounded-md border border-dashed border-gray-300/50 bg-gray-500/10 p-4'
                style={{ minWidth: 200, minHeight: 200 }}
            >
                {/* Group header */}
                <div className='mb-2 flex items-center justify-between'>
                    <input
                        className='rounded border-none bg-transparent px-1 text-sm font-medium text-gray-700 focus:outline-none focus:ring-1 focus:ring-gray-300'
                        value={data?.label || 'Subject Group'}
                        onChange={onLabelChange}
                        placeholder='Subject Group'
                    />
                </div>

                {/* Connection handles */}
                <Handle
                    type='source'
                    position={Position.Top}
                    id='top-source'
                    className='!-top-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Top}
                    id='top-target'
                    className='!-top-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='source'
                    position={Position.Bottom}
                    id='bottom-source'
                    className='!-bottom-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Bottom}
                    id='bottom-target'
                    className='!-bottom-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='source'
                    position={Position.Left}
                    id='left-source'
                    className='!-left-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Left}
                    id='left-target'
                    className='!-left-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='source'
                    position={Position.Right}
                    id='right-source'
                    className='!-right-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Right}
                    id='right-target'
                    className='!-right-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
            </div>
        </>
    );
};

export default memo(SubjectGroup);
export { SubjectGroup };
