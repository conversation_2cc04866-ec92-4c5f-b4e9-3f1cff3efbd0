import { create } from 'zustand';
import { Node } from '@xyflow/react';
import { SectionConfig } from '../Map/interfaces/SectionConfig';
import { useApiStore, NodeSection } from '@repo/api';
import { useMapStore } from '../Map/MapStore';

// Define the node store state interface
interface NodeStoreState {
    // Node event handlers
    onNodeDrag: (event: React.MouseEvent, node: Node) => void;
    onNodeDragStop: (event: React.MouseEvent, node: Node) => void;

    // Concept node state
    loadingSections: Record<string, boolean>;
    generationError: Record<string, string>;
    showAddBar: Record<string, boolean>;
    editingSectionLabel: { sectionId: string; label: string } | null;

    // Methods for concept node
    updateNodeData: (
        nodeId: string,
        data: Record<string, any>,
        debouncedUpdateMap?: () => void
    ) => void;
    generateSectionContentWithLLM: (
        nodeId: string,
        section: SectionConfig,
        conceptTitle: string,
        sections: Record<string, SectionConfig>,
        debouncedUpdateMap?: () => void
    ) => Promise<void>;
    startEditingSectionLabel: (sectionId: string, label: string) => void;
    stopEditingSectionLabel: () => void;
    setShowAddBar: (nodeId: string, show: boolean) => void;
    setLoadingSection: (sectionId: string, isLoading: boolean) => void;
    setGenerationError: (sectionId: string, error: string | null) => void;
}

export const useNodeStore = create<NodeStoreState>((set, get) => ({
    // Node event handlers
    onNodeDrag: (_event, node) => {
        // Implementation will be added later
        console.log('[NodeStore:onNodeDrag] Node drag event:', node.id);
    },

    onNodeDragStop: (_event, node) => {
        // Implementation will be added later
        console.log('[NodeStore:onNodeDragStop] Node drag stop event:', node.id);

        // Update the map in the database
        const mapStore = useMapStore.getState();
        mapStore.debouncedUpdateMap();
    },

    // Concept node state
    loadingSections: {},
    generationError: {},
    showAddBar: {},
    editingSectionLabel: null,

    // Methods for concept node
    updateNodeData: (nodeId, data, debouncedUpdateMap) => {
        console.log(`[NodeStore:updateNodeData] Updating node ${nodeId} with data:`, data);

        // Get the map store to update node data
        const mapStore = useMapStore.getState();

        // Update the node data in the nodes array
        mapStore.setNodes(prevNodes =>
            prevNodes.map(node => {
                if (node.id === nodeId) {
                    return {
                        ...node,
                        data: {
                            ...node.data,
                            ...data
                        }
                    };
                }
                return node;
            })
        );

        // If debouncedUpdateMap is provided, call it to persist changes
        if (debouncedUpdateMap) {
            console.log(`[NodeStore:updateNodeData] Calling debouncedUpdateMap to persist changes`);
            debouncedUpdateMap();
        } else {
            // Use the map store's debounced update
            mapStore.debouncedUpdateMap();
        }
    },

    generateSectionContentWithLLM: async (
        nodeId,
        section,
        conceptTitle,
        sections,
        debouncedUpdateMap
    ) => {
        try {
            console.log(
                `[NodeStore:generateSectionContentWithLLM] Generating content for section in node ${nodeId}:`,
                section
            );

            // Set loading state for this section
            set(state => ({
                loadingSections: { ...state.loadingSections, [section.id]: true }
            }));

            // Clear any previous errors
            set(state => {
                const newErrors = { ...state.generationError };
                delete newErrors[section.id];
                return { generationError: newErrors };
            });

            // Convert sections to the format expected by the API
            const apiSections: Record<string, NodeSection> = {};
            Object.entries(sections).forEach(([key, sectionConfig]) => {
                apiSections[key] = {
                    id: sectionConfig.id,
                    label: sectionConfig.label,
                    icon: sectionConfig.icon as string,
                    value: sectionConfig.value
                };
            });

            // Set up the API store with the necessary data
            const apiStore = useApiStore.getState();
            apiStore.setSourceConceptTitle(conceptTitle);
            apiStore.setSourceSections(apiSections);
            apiStore.setRequestType('section');
            apiStore.setSectionType(section.label);
            apiStore.setCurrentNodeId(nodeId);

            // Generate the section content
            await apiStore.generate();

            // Get the generated content and heading
            const generatedContent = apiStore.generatedSection;
            const generatedHeading = apiStore.generatedSectionHeading;

            if (generatedContent) {
                // Create a complete section object with all properties preserved
                // Use the generated heading if available, otherwise keep the original label
                const updatedSection: SectionConfig = {
                    id: section.id,
                    label: generatedHeading || section.label,
                    icon: section.icon,
                    value: generatedContent,
                    collapsible: section.collapsible ?? true
                };

                // Update the sections object
                const updatedSections = {
                    ...sections,
                    [section.id]: updatedSection
                };

                console.log(
                    `[NodeStore:generateSectionContentWithLLM] Updated section ${section.id}:`,
                    updatedSection
                );

                console.log(
                    `[NodeStore:generateSectionContentWithLLM] Generated content:`,
                    generatedContent
                );

                // Call the updateNodeData method to update the node
                get().updateNodeData(nodeId, { sections: updatedSections }, debouncedUpdateMap);
            } else {
                throw new Error('No content was generated');
            }
        } catch (error) {
            console.error('Error generating section content:', error);
            set(state => ({
                generationError: {
                    ...state.generationError,
                    [section.id]: 'Failed to generate content. Click to try again.'
                }
            }));
        } finally {
            // Clear loading state
            set(state => {
                const newLoading = { ...state.loadingSections };
                delete newLoading[section.id];
                return { loadingSections: newLoading };
            });
        }
    },

    startEditingSectionLabel: (sectionId, label) => {
        set({ editingSectionLabel: { sectionId, label } });
    },

    stopEditingSectionLabel: () => {
        set({ editingSectionLabel: null });
    },

    setShowAddBar: (nodeId, show) => {
        set(state => ({
            showAddBar: { ...state.showAddBar, [nodeId]: show }
        }));
    },

    setLoadingSection: (sectionId, isLoading) => {
        set(state => {
            if (isLoading) {
                return {
                    loadingSections: { ...state.loadingSections, [sectionId]: true }
                };
            } else {
                const newLoading = { ...state.loadingSections };
                delete newLoading[sectionId];
                return { loadingSections: newLoading };
            }
        });
    },

    setGenerationError: (sectionId, error) => {
        set(state => {
            if (error) {
                return {
                    generationError: { ...state.generationError, [sectionId]: error }
                };
            } else {
                const newErrors = { ...state.generationError };
                delete newErrors[sectionId];
                return { generationError: newErrors };
            }
        });
    }
}));
