import { callChatGPT4oMini, ConceptSuggestion, NodeSection } from './openai';
import { NODE_SECTIONS, EDGE_TYPES } from '@repo/utils';
import { triplit, useAuth } from '@repo/db';
import { create } from 'zustand';
import { FlashcardType, Flashcard } from './utils/flashcard-generator.js';
import { generateConceptsPrompt } from './prompts/generateConceptsPrompt';
import { generateFlashcardsPrompt } from './prompts/generateFlashcardPrompt';
import { generateSection } from './prompts/generateSection';
import { useGlobalStore } from '@repo/utils';
import { saveFlashcardsToTriplit } from './utils/saveFlashcardsToTriplit.js';

// Define the request types
export type RequestType = 'concepts' | 'flashcards' | 'section';

// Define the store state interface
type ApiStoreType = {
    // Store parameters
    sourceConceptTitle: string;
    existingConceptTitles: string[];
    sourceSections: Record<string, NodeSection>;
    requestType: RequestType;
    customPrompt: string | null;
    sectionType: string | null; // For section generation
    currentNodeId: string | null; // Source node ID for concept suggestions
    temperature: number | null; // Temperature for AI generation
    maxTokens: number | null; // Max tokens for AI generation

    // Store state
    suggestions: ConceptSuggestion[];
    flashcards: Flashcard[];
    generatedSection: string | null;
    generatedSectionHeading: string | null; // Store the heading separately
    isLoading: boolean;
    error: string | null;
    deckData: {
        name?: string;
        learning_outcome?: string;
        language?: string;
    } | null;
    isFetchingDeck: boolean;
    deckError: Error | null;

    // Actions
    setSourceConceptTitle: (title: string) => void;
    setExistingConceptTitles: (titles: string[]) => void;
    setSourceSections: (sections: Record<string, NodeSection>) => void;
    setSuggestions: (suggestions: ConceptSuggestion[]) => void;
    setFlashcards: (flashcards: Flashcard[]) => void;
    setGeneratedSection: (section: string | null) => void;
    setIsLoading: (isLoading: boolean) => void;
    setError: (error: string | null) => void;
    setDeckData: (
        data: { name?: string; learning_outcome?: string; language?: string } | null
    ) => void;
    setIsFetchingDeck: (isFetching: boolean) => void;
    setDeckError: (error: Error | null) => void;
    setRequestType: (type: RequestType) => void;
    setCustomPrompt: (prompt: string | null) => void;
    setSectionType: (type: string | null) => void;
    setCurrentNodeId: (nodeId: string | null) => void;
    setTemperature: (temperature: number | null) => void;
    setMaxTokens: (maxTokens: number | null) => void;

    // Main function
    generate: () => Promise<void>;

    // Fetch deck data from Triplit
    fetchDeckData: () => Promise<void>;
};

export const useApiStore = create<ApiStoreType>((set, get) => ({
    sourceConceptTitle: '',
    existingConceptTitles: [],
    sourceSections: {},
    requestType: 'concepts',
    customPrompt: null,
    sectionType: null,
    currentNodeId: null,
    temperature: null,
    maxTokens: null,
    suggestions: [],
    flashcards: [],
    generatedSection: null,
    generatedSectionHeading: null,
    isLoading: false,
    error: null,
    deckData: null,
    isFetchingDeck: false,
    deckError: null,

    // Actions to update state
    setSourceConceptTitle: title => set({ sourceConceptTitle: title }),
    setExistingConceptTitles: titles => set({ existingConceptTitles: titles }),
    setSourceSections: sections => set({ sourceSections: sections }),
    setSuggestions: suggestions => set({ suggestions }),
    setFlashcards: flashcards => set({ flashcards }),
    setGeneratedSection: (section: string | null) => set({ generatedSection: section }),
    setGeneratedSectionHeading: (heading: string | null) =>
        set({ generatedSectionHeading: heading }),
    setIsLoading: isLoading => set({ isLoading }),
    setError: error => set({ error }),
    setDeckData: data => set({ deckData: data }),
    setIsFetchingDeck: isFetching => set({ isFetchingDeck: isFetching }),
    setDeckError: error => set({ deckError: error }),
    setRequestType: type => set({ requestType: type }),
    setCustomPrompt: prompt => set({ customPrompt: prompt }),
    setSectionType: type => set({ sectionType: type }),
    setCurrentNodeId: nodeId => set({ currentNodeId: nodeId }),
    setTemperature: temperature => set({ temperature }),
    setMaxTokens: maxTokens => set({ maxTokens }),

    // Fetch deck data from Triplit
    fetchDeckData: async () => {
        const { activeDeckId: mapId } = useGlobalStore.getState();

        set({ isFetchingDeck: true, deckError: null });

        try {
            if (!mapId) {
                console.warn('No active deck ID found');
                set({
                    isFetchingDeck: false,
                    deckData: null
                });
                return;
            }

            // Fetch deck data directly using Triplit client
            const deck = await triplit.fetchById('decks', mapId);
            set({
                deckData: {
                    name: deck?.name,
                    learning_outcome: deck?.learning_outcome,
                    language: deck?.language || 'en'
                },
                isFetchingDeck: false
            });
        } catch (error) {
            console.error('Error fetching deck data:', error);
            set({
                deckError: error instanceof Error ? error : new Error('Failed to fetch deck data'),
                isFetchingDeck: false,
                deckData: null
            });
        }
    },

    // Main function to generate content based on request type
    generate: async () => {
        const {
            sourceConceptTitle,
            existingConceptTitles,
            sourceSections,
            deckData,
            requestType,
            customPrompt,
            sectionType,
            temperature,
            maxTokens
        } = get();

        set({ isLoading: true, error: null });

        // Fetch deck data first
        await get().fetchDeckData();

        try {
            const {
                activeDeckLearningOutcome: learningOutcome,
                activeDeckName: deckTitle,
                activeDeckId: mapId,
                activeDeckLanguage: deckLanguage
            } = useGlobalStore.getState();

            // Check if we have a valid deck context for generation
            if (!mapId) {
                console.warn(
                    '[useApiStore:generate] No active deck ID found, cannot generate content'
                );
                set({
                    error: 'No active deck found. Please select or create a deck first.',
                    isLoading: false
                });
                return;
            }

            console.log(
                '[useApiStore:generate] \n\ndeckTitle:',
                deckTitle,
                '\nDeck data:',
                deckData,
                '\nLearning Outcome:',
                learningOutcome,
                '\nMap ID:',
                mapId,
                '\nLanguage:',
                deckLanguage || 'en'
            );

            let prompt = '';
            let response;

            // Use custom prompt if provided, otherwise generate based on request type
            if (customPrompt) {
                prompt = customPrompt;
            } else {
                switch (requestType) {
                    case 'concepts':
                        prompt = generateConceptsPrompt(
                            sourceConceptTitle,
                            existingConceptTitles,
                            sourceSections,
                            deckTitle || undefined,
                            learningOutcome || undefined,
                            temperature ?? undefined,
                            maxTokens ?? undefined
                        );
                        console.log('generate: Concepts prompt:\n', prompt);
                        break;
                    case 'flashcards':
                        prompt = generateFlashcardsPrompt(
                            sourceConceptTitle,
                            sourceSections,
                            deckTitle || undefined,
                            learningOutcome || undefined,
                            deckLanguage || 'en',
                            temperature ?? undefined,
                            maxTokens ?? undefined
                        );
                        console.log('generate: Flashcards prompt:\n', prompt);
                        break;
                    case 'section':
                        if (!sectionType) {
                            throw new Error('Section type is required for section generation');
                        }
                        // Find the section definition to get default temperature and maxTokens
                        const sectionKey = Object.keys(NODE_SECTIONS).find(
                            key =>
                                NODE_SECTIONS[key as keyof typeof NODE_SECTIONS].defaultTitle ===
                                sectionType
                        ) as keyof typeof NODE_SECTIONS | undefined;

                        // Get section-specific temperature and maxTokens if available
                        const sectionTemperature =
                            sectionKey && 'temperature' in NODE_SECTIONS[sectionKey]
                                ? NODE_SECTIONS[sectionKey].temperature
                                : undefined;
                        const sectionMaxTokens =
                            sectionKey && 'maxTokens' in NODE_SECTIONS[sectionKey]
                                ? NODE_SECTIONS[sectionKey].maxTokens
                                : undefined;

                        prompt = generateSection(
                            sourceConceptTitle,
                            sectionType,
                            sourceSections,
                            deckTitle || undefined,
                            learningOutcome || undefined,
                            deckLanguage || 'en',
                            temperature ?? sectionTemperature,
                            maxTokens ?? sectionMaxTokens
                        );
                        console.log('generate: Section prompt:\n', prompt);
                        break;
                    default:
                        throw new Error(`Unknown request type: ${requestType}`);
                }
            }

            // Call the OpenAI API
            response = await callChatGPT4oMini(prompt, {
                temperature: temperature ?? 0.7, // Use store value or default
                maxTokens: maxTokens ?? 2500 // Use store value or default
            });

            // Process the response based on request type
            switch (requestType) {
                case 'concepts':
                    try {
                        // Parse the response content
                        const parsedConcepts = JSON.parse(response.content) as ConceptSuggestion[];

                        // Ensure each concept has a unique ID
                        const conceptsWithIds = parsedConcepts.map(concept => ({
                            ...concept,
                            id: concept.id || crypto.randomUUID(),
                            sections: concept.sections || {}
                        }));

                        console.log('[useApiStore] Processed concepts with IDs:', conceptsWithIds);

                        set({ suggestions: conceptsWithIds, isLoading: false });
                    } catch (error) {
                        console.error('[useApiStore] Error processing concepts:', error);
                        set({
                            error: 'Failed to process concept suggestions',
                            isLoading: false
                        });
                    }
                    break;
                case 'flashcards':
                    const parsedFlashcards = JSON.parse(response.content) as Flashcard[];
                    set({ flashcards: parsedFlashcards, isLoading: false });

                    // Save the flashcards to Triplit if we have a node ID
                    const { currentNodeId } = get();
                    if (currentNodeId) {
                        try {
                            const { activeDeckId } = useGlobalStore.getState();

                            // Get the current user from the auth store
                            const user = useAuth.getState().user;

                            if (user && activeDeckId) {
                                console.log('Saving flashcards to Triplit:', {
                                    conceptId: currentNodeId,
                                    flashcards: parsedFlashcards.length,
                                    userId: user.id,
                                    deckId: activeDeckId
                                });

                                await saveFlashcardsToTriplit(
                                    currentNodeId,
                                    parsedFlashcards,
                                    user.id,
                                    activeDeckId
                                );

                                console.log('Flashcards saved successfully');
                            } else {
                                console.error('Missing user or deck ID:', { user, activeDeckId });
                            }
                        } catch (error) {
                            console.error('Error saving flashcards to Triplit:', error);
                        }
                    }
                    break;
                case 'section':
                    // For section generation, extract the heading and content
                    const lines = response.content.split('\n');
                    let heading = '';
                    let content = '';

                    if (lines.length > 0) {
                        // First line is the heading
                        heading = lines[0].trim();

                        // Skip the first two lines (heading and blank line) and join the rest
                        content = lines.slice(2).join('\n').trim();

                        console.log('Generated section heading:', heading);
                        console.log('Generated section content:', content);
                    } else {
                        // Fallback if the format is not as expected
                        content = response.content;
                    }

                    set({
                        generatedSection: content,
                        generatedSectionHeading: heading,
                        isLoading: false
                    });
                    break;
            }
        } catch (error) {
            console.error(`Error generating ${requestType}:`, error);
            set({
                error: `Failed to generate ${requestType}`,
                isLoading: false
            });
            throw error;
        }
    }
}));
