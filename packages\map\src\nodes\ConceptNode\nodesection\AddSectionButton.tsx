import React, { useState } from 'react';
import { PlusCircle } from 'lucide-react';
import { NODE_SECTIONS } from '@repo/utils';
import {
    Button,
    Popover,
    PopoverTrigger,
    PopoverContent,
    Command,
    CommandEmpty,
    CommandGroup,
    CommandItem,
    CommandList
} from '@repo/ui';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';
import { handleAddSectionSelect } from './handleAddSectionSelect';

export interface NodeAddSectionButtonProps {
    conceptTitle: string;
    sections: Record<string, SectionConfig>;
    tags: { name: string; type: string }[];
    showAddBar: boolean;
    startEditingSectionLabel: (sectionId: string, label: string) => void;
    generateSectionContentWithLLM: (section: SectionConfig) => Promise<void>;
    updateNodeData: (data: Record<string, any>) => void;
    nodeId: string;
}

export const NodeAddSectionButton: React.FC<NodeAddSectionButtonProps> = ({
    conceptTitle,
    sections,
    tags,
    showAddBar,
    startEditingSectionLabel,
    generateSectionContentWithLLM,
    updateNodeData,
    nodeId
}) => {
    const [sectionSelectPopoverOpen, setSectionSelectPopoverOpen] = useState(false);

    return (
        <div
            className={`mt-3 flex items-center justify-center transition-opacity duration-300 ${
                showAddBar ? 'opacity-100' : 'pointer-events-none opacity-0'
            }`}
        >
            {/* Section Selection Popover Trigger */}
            <Popover open={sectionSelectPopoverOpen} onOpenChange={setSectionSelectPopoverOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant='outline'
                        size='sm'
                        className='flex w-full items-center justify-center gap-1 border-dashed border-gray-300 text-xs text-gray-500 hover:border-gray-400 hover:text-gray-700'
                        onClick={() => {
                            // Check if title exists before opening
                            if (!conceptTitle || conceptTitle === 'New Concept Title') {
                                alert('Please set a concept title before adding sections.');
                                return; // Prevent opening popover
                            }
                            setSectionSelectPopoverOpen(true);
                        }}
                    >
                        <PlusCircle size={14} /> Add Section
                    </Button>
                </PopoverTrigger>
                <PopoverContent className='w-[250px] p-0' align='center'>
                    <Command>
                        {/* Optional: Add CommandInput if list becomes long */}
                        <CommandList>
                            <CommandEmpty>No section types found.</CommandEmpty>
                            {/* Custom Section Option */}
                            <CommandGroup heading='Custom'>
                                <CommandItem
                                    key='custom'
                                    value='custom'
                                    onSelect={() =>
                                        handleAddSectionSelect(
                                            'custom',
                                            conceptTitle,
                                            sections,
                                            tags,
                                            nodeId,
                                            startEditingSectionLabel,
                                            generateSectionContentWithLLM,
                                            updateNodeData,
                                            setSectionSelectPopoverOpen
                                        )
                                    }
                                    className='flex items-center gap-2 text-sm'
                                    title='Create a custom section with your own title'
                                >
                                    <span>📝</span>
                                    <span>Add Custom</span>
                                </CommandItem>
                            </CommandGroup>
                            <CommandGroup heading='Section Types'>
                                {Object.entries(NODE_SECTIONS).map(([key, config]) => {
                                    // Skip Core Idea if it already exists
                                    if (key === 'coreIdea') {
                                        const hasCoreIdea = Object.values(sections || {}).some(
                                            section => section.label === config.defaultTitle
                                        );
                                        if (hasCoreIdea) return null;
                                    }

                                    return (
                                        <CommandItem
                                            key={key}
                                            value={key}
                                            onSelect={() =>
                                                handleAddSectionSelect(
                                                    key,
                                                    conceptTitle,
                                                    sections,
                                                    tags,
                                                    nodeId,
                                                    startEditingSectionLabel,
                                                    generateSectionContentWithLLM,
                                                    updateNodeData,
                                                    setSectionSelectPopoverOpen
                                                )
                                            }
                                            className='flex items-center gap-2 text-sm'
                                            title={`Add ${config.defaultTitle} section`}
                                        >
                                            <span>{config.icon}</span>
                                            <span>{config.defaultTitle}</span>
                                        </CommandItem>
                                    );
                                })}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
    );
};
