import { create } from 'zustand';
import { Node } from '@xyflow/react';

// Define the context menu store state interface
interface ContextMenuStoreState {
    // Context menu event handlers
    onContextMenu: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
    onNodeContextMenu: (event: React.MouseEvent, node: Node) => void;
}

export const useContextMenuStore = create<ContextMenuStoreState>((set, get) => ({
    onContextMenu: event => {
        // Implementation will be added later
        console.log('[ContextMenuStore:onContextMenu] Context menu event:', event);
    },

    onNodeContextMenu: (_event, node) => {
        // Implementation will be added later
        console.log('[ContextMenuStore:onNodeContextMenu] Node context menu event:', node.id);
    }
}));
