import sharedConfig from '@repo/vite-config';
import path from 'path';
import { defineConfig, mergeConfig, loadEnv } from 'vite';
import wasm from 'vite-plugin-wasm';
import topLevelAwait from 'vite-plugin-top-level-await';

export default defineConfig(configEnv => {
    // Load env file based on mode
    const env = loadEnv(configEnv.mode, process.cwd(), '');

    return mergeConfig(
        sharedConfig({
            env: configEnv,
            lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
            ssrInput: './server/app.ts',
            testSetupFiles: './src/setupTest.ts'
        }),
        defineConfig({
            // Define environment variables to be replaced in the browser
            define: {
                // Expose OpenAI API key to the browser
                'import.meta.env.OPENAI_API_KEY': JSON.stringify(env.OPENAI_API_KEY),
                // Also expose with VITE_ prefix for consistency
                'import.meta.env.VITE_OPENAI_API_KEY': JSON.stringify(
                    env.VITE_OPENAI_API_KEY || env.OPENAI_API_KEY
                )
            },
            resolve: {
                alias: {
                    '@': path.resolve(__dirname, './src')
                }
            },
            // Add plugins for WASM and top-level await support (needed for Triplit)
            plugins: [wasm(), topLevelAwait()]
        }),
        false
    );
});
