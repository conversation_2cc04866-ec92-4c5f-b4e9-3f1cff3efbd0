import { Node, Edge, Connection, MarkerType, addEdge } from '@xyflow/react';

/**
 * Handles the selection of an edge type when creating a connection between nodes
 * @param edgeType The type of edge to create
 * @param selectedNodeToLink The target node to link to
 * @param sourceNodeId The source node ID
 * @param getNode Function to get a node by ID
 * @param setEdges Function to update edges
 * @param setEdgeTypePopoverOpen Function to control edge type popover visibility
 * @param setSelectedNodeToLink Function to set the selected node
 * @param setLinkSearchQuery Function to update link search query
 */
export const handleEdgeTypeSelect = (
  edgeType: string,
  selectedNodeToLink: Node | null,
  sourceNodeId: string,
  getNode: (id: string) => Node | undefined,
  setEdges: (updater: (edges: Edge[]) => Edge[]) => void,
  setEdgeTypePopoverOpen: (open: boolean) => void,
  setSelectedNodeToLink: (node: Node | null) => void,
  setLinkSearchQuery: (query: string) => void
) => {
  if (!selectedNodeToLink) return;

  const targetNodeId = selectedNodeToLink.id;
  console.log(
    `[packages/ui/map/features/conceptnode/utils/handleEdgeTypeSelect.ts] Creating edge from ${sourceNodeId} to ${targetNodeId} with type ${edgeType}`
  );

  // Ensure sourceNodeId is treated as string if necessary
  const validatedSourceNodeId = String(sourceNodeId);

  // Calculate the best handles based on node positions
  const sourceNode = getNode(validatedSourceNodeId);
  const targetNode = getNode(targetNodeId);

  let sourceHandle = 'right-source'; // Default source handle
  let targetHandle = 'left-target'; // Default target handle

  if (sourceNode && targetNode) {
    // Calculate the angle between nodes to determine the best handles
    const sourceX = sourceNode.position.x + (sourceNode.width || 0) / 2;
    const sourceY = sourceNode.position.y + (sourceNode.height || 0) / 2;
    const targetX = targetNode.position.x + (targetNode.width || 0) / 2;
    const targetY = targetNode.position.y + (targetNode.height || 0) / 2;

    // Calculate angle from source to target
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    const angle = Math.atan2(dy, dx) * (180 / Math.PI);

    // Determine the best source handle based on the angle
    if (angle >= -45 && angle < 45) {
      sourceHandle = 'right-source'; // Right side
    } else if (angle >= 45 && angle < 135) {
      sourceHandle = 'bottom-source'; // Bottom side
    } else if (angle >= 135 || angle < -135) {
      sourceHandle = 'left-source'; // Left side
    } else {
      sourceHandle = 'top-source'; // Top side
    }

    // Calculate angle from target to source (reverse direction)
    const reverseAngle = Math.atan2(-dy, -dx) * (180 / Math.PI);

    // Determine the best target handle based on the reverse angle
    if (reverseAngle >= -45 && reverseAngle < 45) {
      targetHandle = 'right-target'; // Right side
    } else if (reverseAngle >= 45 && reverseAngle < 135) {
      targetHandle = 'bottom-target'; // Bottom side
    } else if (reverseAngle >= 135 || reverseAngle < -135) {
      targetHandle = 'left-target'; // Left side
    } else {
      targetHandle = 'top-target'; // Top side
    }
  }

  const newEdge: Connection & { type?: string; data?: any; markerEnd?: any } = {
    source: validatedSourceNodeId,
    target: targetNodeId,
    type: edgeType,
    data: { edgeType: edgeType },
    sourceHandle: sourceHandle,
    targetHandle: targetHandle,
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#d9d9d9', // Match the lighter grey default edge color
      width: 20, // Slightly larger for better visibility
      height: 20, // Slightly larger for better visibility
      strokeWidth: 2 // Add stroke width for better visibility
    } // Add arrow marker by default
  };

  setEdges(eds => addEdge(newEdge, eds));

  // Reset state
  setEdgeTypePopoverOpen(false);
  setSelectedNodeToLink(null);
  setLinkSearchQuery('');
};
