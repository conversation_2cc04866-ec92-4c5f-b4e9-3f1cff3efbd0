import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { supabase } from '@repo/auth';
import {
    <PERSON><PERSON>,
    Card,
    CardHeader,
    CardContent,
    CardTitle,
    CardDescription,
    Form,
    FormField,
    FormItem,
    FormControl,
    FormLabel,
    FormDescription,
    Alert,
    AlertTitle,
    AlertDescription,
    Switch,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@repo/ui';
import { TbShield } from 'react-icons/tb';

// Schema for privacy settings
const privacySchema = z.object({
    profileVisibility: z.string().default('public'),
    showActivity: z.boolean().default(true),
    allowDataCollection: z.boolean().default(true)
});

type PrivacyFormValues = z.infer<typeof privacySchema>;

interface PrivacyFormProps {
    user: any;
}

export default function PrivacyForm({ user }: PrivacyFormProps) {
    const [privacySuccess, setPrivacySuccess] = useState<string | null>(null);
    const [privacyError, setPrivacyError] = useState<string | null>(null);
    const [isPrivacyLoading, setIsPrivacyLoading] = useState(false);

    // Privacy form
    const privacyForm = useForm<PrivacyFormValues>({
        resolver: zodResolver(privacySchema),
        defaultValues: {
            profileVisibility: user?.user_metadata?.profileVisibility || 'public',
            showActivity: user?.user_metadata?.showActivity !== false,
            allowDataCollection: user?.user_metadata?.allowDataCollection !== false
        }
    });

    // Handle privacy settings update
    async function onPrivacySubmit(values: PrivacyFormValues) {
        setIsPrivacyLoading(true);
        setPrivacySuccess(null);
        setPrivacyError(null);

        try {
            const { error } = await supabase.auth.updateUser({
                data: {
                    profileVisibility: values.profileVisibility,
                    showActivity: values.showActivity,
                    allowDataCollection: values.allowDataCollection
                }
            });

            if (error) {
                throw error;
            }

            setPrivacySuccess('Privacy settings updated successfully');
        } catch (error: any) {
            setPrivacyError(error.message || 'Failed to update privacy settings');
        } finally {
            setIsPrivacyLoading(false);
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                    <TbShield className='h-5 w-5 text-primary' />
                    Privacy Settings
                </CardTitle>
                <CardDescription>Control your privacy preferences</CardDescription>
            </CardHeader>
            <CardContent>
                <Form {...privacyForm}>
                    <form
                        onSubmit={privacyForm.handleSubmit(onPrivacySubmit)}
                        className='space-y-4'
                    >
                        <FormField
                            control={privacyForm.control}
                            name='profileVisibility'
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Profile Visibility</FormLabel>
                                    <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder='Select visibility' />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value='public'>Public</SelectItem>
                                            <SelectItem value='friends'>Friends Only</SelectItem>
                                            <SelectItem value='private'>Private</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <FormDescription>
                                        Control who can see your profile
                                    </FormDescription>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={privacyForm.control}
                            name='showActivity'
                            render={({ field }) => (
                                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                                    <div className='space-y-0.5'>
                                        <FormLabel className='text-base'>Activity Status</FormLabel>
                                        <FormDescription>
                                            Show your activity status to others
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={privacyForm.control}
                            name='allowDataCollection'
                            render={({ field }) => (
                                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                                    <div className='space-y-0.5'>
                                        <FormLabel className='text-base'>Data Collection</FormLabel>
                                        <FormDescription>
                                            Allow us to collect usage data to improve your
                                            experience
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        {privacySuccess && (
                            <Alert className='border-green-200 bg-green-50'>
                                <AlertTitle>Success</AlertTitle>
                                <AlertDescription>{privacySuccess}</AlertDescription>
                            </Alert>
                        )}

                        {privacyError && (
                            <Alert className='border-red-200 bg-red-50'>
                                <AlertTitle>Error</AlertTitle>
                                <AlertDescription>{privacyError}</AlertDescription>
                            </Alert>
                        )}

                        <Button type='submit' disabled={isPrivacyLoading}>
                            {isPrivacyLoading ? 'Saving...' : 'Save Privacy Settings'}
                        </Button>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
}
