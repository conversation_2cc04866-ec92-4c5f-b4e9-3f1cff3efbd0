import React, { memo, useEffect } from 'react';
import { <PERSON>le, Position, NodeResizer, NodeProps, useReactFlow } from '@xyflow/react';
import { GROUP_TYPES } from '@repo/utils';

export interface TopicGroupData {
    label: string;
}

const TopicGroup = ({ id, data, selected, style }: NodeProps & { style?: React.CSSProperties }) => {
    const reactFlowInstance = useReactFlow();
    const { setNodes, getNodes } = reactFlowInstance;

    // Log when the component is rendered with style information
    console.log(`[TopicGroup] Rendering topic group ${id} with z-index: -1`, {
        style,
        width: style?.width,
        height: style?.height
    });

    // Log child nodes when the component is rendered
    useEffect(() => {
        const nodes = getNodes();
        const childNodes = nodes.filter(node => node.parentId === id);
        console.log(
            `[TopicGroup] Group ${id} has ${childNodes.length} child nodes:`,
            childNodes.map(node => ({
                id: node.id,
                position: node.position,
                width: node.width,
                height: node.height
            }))
        );
    }, [id, getNodes, reactFlowInstance]);

    // Function to handle label change
    const onLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        console.log(`[TopicGroup] Label changed for group ${id}: ${e.target.value}`);
        setNodes(nodes =>
            nodes.map(node => {
                if (node.id === id) {
                    return {
                        ...node,
                        data: {
                            ...node.data,
                            label: e.target.value
                        }
                    };
                }
                return node;
            })
        );
    };

    return (
        <>
            {/* Add NodeResizer for resizability */}
            <NodeResizer
                minWidth={320}
                minHeight={240}
                isVisible={selected}
                lineClassName='border-blue-300'
                handleClassName='bg-white border-blue-300'
            />

            {/* Group container */}
            <div
                className='h-full w-full rounded-md border border-dashed border-blue-300/50 bg-blue-500/10 p-4'
                style={{ minWidth: 320, minHeight: 240, position: 'relative', zIndex: -1 }}
            >
                {/* Group header */}
                <div className='mb-2 flex items-center justify-between'>
                    <input
                        className='rounded border-none bg-transparent px-1 text-sm font-medium text-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-300'
                        value={(data?.label as string) || 'Topic Group'}
                        onChange={onLabelChange}
                        placeholder='Topic Group'
                    />
                </div>

                {/* Connection handles */}
                <Handle
                    type='source'
                    position={Position.Top}
                    id='top-source'
                    className='!-top-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Top}
                    id='top-target'
                    className='!-top-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='source'
                    position={Position.Bottom}
                    id='bottom-source'
                    className='!-bottom-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Bottom}
                    id='bottom-target'
                    className='!-bottom-1 !h-1.5 !min-h-0 !w-3 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='source'
                    position={Position.Left}
                    id='left-source'
                    className='!-left-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Left}
                    id='left-target'
                    className='!-left-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='source'
                    position={Position.Right}
                    id='right-source'
                    className='!-right-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
                <Handle
                    type='target'
                    position={Position.Right}
                    id='right-target'
                    className='!-right-1 !h-3 !min-h-0 !w-1.5 !min-w-0 !border-transparent !bg-transparent hover:!opacity-100'
                    style={{ opacity: 0.3 }}
                />
            </div>
        </>
    );
};

export default memo(TopicGroup);
export { TopicGroup };
