import { NodeSection } from '../openai';
import { EDGE_TYPES } from '@repo/utils';
import { getLearningOutcome, formatSectionLabels, getExpertIntro } from './promptWrapper';

// Helper function to generate a prompt for finding related concepts
export const generateConceptsPrompt = (
    sourceConceptTitle: string,
    existingConceptTitles: string[],
    sourceSections: Record<string, NodeSection>,
    deckTitle?: string,
    learningOutcome?: string,
    temperature?: number,
    maxTokens?: number
): string => {
    // Format existing concepts for context
    const existingConceptsText =
        existingConceptTitles.length > 0
            ? `Existing concepts in the map (DO NOT suggest any of these):\n${existingConceptTitles.map(title => `- ${title}`).join('\n')}`
            : 'No other concepts exist in the map yet.';

    // Create a guide for edge types with titles, descriptions and examples
    const edgeTypeGuide = Object.entries(EDGE_TYPES)
        .map(([key, value]) => {
            return `- ${value.title} (${key}): ${value.description}\n  Example: ${value.example}`;
        })
        .join('\n\n');

    // Format existing sections
    const existingSectionLabels = formatSectionLabels(sourceSections);

    // Build the prompt
    return `${getExpertIntro('creating comprehensive concept maps for learning')} Your task is to identify SPECIFIC, CONCRETE, and IMPORTANT concepts that are directly related to "${sourceConceptTitle}" ${deckTitle ? ` in the SPECIFIC CONTEXT of "${deckTitle}"` : ''}.

LEARNING OUTCOME ANALYSIS:
"${learningOutcome}"

IMPORTANT GUIDELINES:
1. Identify PRECISE, TECHNICAL concepts that are DIRECTLY related to understanding "${sourceConceptTitle}" - avoid vague or overly general concepts
2. Include PREREQUISITE concepts that must be understood before fully grasping "${sourceConceptTitle}" (use "requires" or "is required by" relationships)
3. Include COMPONENT concepts that are essential parts or elements of "${sourceConceptTitle}" (use "is part of" or "includes" relationships)
4. Include CONTRASTING concepts that help define "${sourceConceptTitle}" by showing what it is not
5. Focus on DEPTH over breadth - prioritize concepts with strong, direct relationships to the source concept

RELATIONSHIP TYPES:
${edgeTypeGuide}

For each suggested concept, provide a title, brief description, and the type of relationship it has with the source concept. The relationship type is critical - it must be one of the defined edge types and accurately represent how the new concept relates to "${sourceConceptTitle}".

EXISTING CONCEPTS:
${existingConceptsText}

SOURCE CONCEPT DETAILS:
The source concept has the following sections: ${existingSectionLabels || 'None yet'}
${existingSectionLabels ? 'Use these sections to deeply understand the nature and scope of the source concept.' : ''}

For each suggested concept, provide:
1. A clear, concise, SPECIFIC title (1-5 words) - use precise technical terminology
2. A brief description (1-2 sentences that highlight why this concept is IMPORTANT to understand "${sourceConceptTitle}")
3. The edge type with the source concept (must be one of: ${Object.keys(EDGE_TYPES).join(', ')})

LEARNING CONTEXT:
${deckTitle ? `Deck: "${deckTitle}"` : 'No specific deck context provided'}
${getLearningOutcome(learningOutcome)}

OUTPUT FORMATTING:
Format your response as a JSON array with objects containing "title", "description", and "edgeType" properties.
Example format:
[
  {
    "title": "Concept Title",
    "description": "Brief description of the concept and why it's important for understanding the source concept.",
    "edgeType": "${Object.keys(EDGE_TYPES)[0]}"
  },
  ...
]

IMPORTANT: Generate at least 10-15 high-quality, SPECIFIC concepts that collectively provide a comprehensive understanding of "${sourceConceptTitle}".
Focus on PRECISE, TECHNICAL, WELL-DEFINED concepts rather than vague, general, or abstract ones.`;
};
