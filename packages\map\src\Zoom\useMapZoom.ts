import { useCallback, useEffect, useState } from 'react';
import { useReactFlow } from '@xyflow/react';
import { ZOOM_LEVELS } from '@repo/utils';

export function useMapZoom() {
    const [zoom, setZoom] = useState<number>(ZOOM_LEVELS.OVERVIEW); // Default zoom level
    const reactFlowInstance = useReactFlow();

    // Function to get the closest predefined zoom level
    const getClosestZoomLevel = useCallback((currentZoom: number): number => {
        const zoomLevels = Object.values(ZOOM_LEVELS).sort((a, b) => a - b);
        if (zoomLevels.length === 0) return currentZoom;

        return zoomLevels.reduce((prev, curr) => {
            return Math.abs(curr - currentZoom) < Math.abs(prev - currentZoom) ? curr : prev;
        }, zoomLevels[0]);
    }, []);

    // Handle wheel events for zooming between predetermined levels
    const handleWheel = useCallback(
        (event: WheelEvent) => {
            event.preventDefault();

            // Determine direction (zoom in or out)
            const zoomingIn = event.deltaY < 0;

            // Get all zoom levels and sort them
            const zoomLevels = Object.values(ZOOM_LEVELS).sort((a, b) => a - b);

            // Get current viewport
            const { zoom: currentZoom } = reactFlowInstance.getViewport();

            // Find the closest current level
            const closestCurrentLevel = getClosestZoomLevel(currentZoom);
            const currentIndex = zoomLevels.indexOf(closestCurrentLevel);

            // Determine target level based on direction
            let targetIndex;
            if (zoomingIn) {
                // Zoom in (move to next higher level)
                targetIndex = Math.min(currentIndex + 1, zoomLevels.length - 1);
            } else {
                // Zoom out (move to next lower level)
                targetIndex = Math.max(currentIndex - 1, 0);
            }

            const targetZoom = zoomLevels[targetIndex];

            // Only zoom if we're changing levels
            if (targetZoom !== closestCurrentLevel) {
                // Get current viewport
                const { x, y } = reactFlowInstance.getViewport();

                if (zoomingIn) {
                    // When zooming in, use the cursor position
                    // Get the mouse position in screen coordinates
                    const mousePosition = {
                        x: event.clientX,
                        y: event.clientY
                    };

                    // Convert screen coordinates to flow coordinates
                    const flowPosition = reactFlowInstance.screenToFlowPosition(mousePosition);

                    // Calculate the new viewport position to keep the mouse position fixed
                    const zoomRatio = targetZoom / currentZoom;
                    const newX = flowPosition.x - (flowPosition.x - x) * zoomRatio;
                    const newY = flowPosition.y - (flowPosition.y - y) * zoomRatio;

                    // Apply the new viewport
                    reactFlowInstance.setViewport(
                        { x: newX, y: newY, zoom: targetZoom },
                        { duration: 400 } // Faster animation
                    );
                } else {
                    // When zooming out, ignore cursor position
                    // But maintain the current viewport position (x, y) to prevent jumping
                    reactFlowInstance.setViewport(
                        { zoom: targetZoom, x, y },
                        { duration: 400 } // Faster animation
                    );
                }

                // Update our local state
                setZoom(targetZoom);
            }
        },
        [reactFlowInstance, getClosestZoomLevel]
    );

    // Function to zoom to the next predefined level
    const zoomIn = useCallback(() => {
        // Get current viewport
        const { zoom: currentZoom, x, y } = reactFlowInstance.getViewport();

        // Get all zoom levels and sort them
        const zoomLevels = Object.values(ZOOM_LEVELS).sort((a, b) => a - b);

        // Find the closest current level
        const closestCurrentLevel = getClosestZoomLevel(currentZoom);
        const currentIndex = zoomLevels.indexOf(closestCurrentLevel);

        // If we're already at the highest zoom level, do nothing
        if (currentIndex === zoomLevels.length - 1) {
            return;
        }

        // Get the center of the viewport in screen coordinates
        const flowElement = document.querySelector('.react-flow');
        if (!flowElement) return;

        const rect = flowElement.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // Convert screen coordinates to flow coordinates
        const flowPosition = reactFlowInstance.screenToFlowPosition({
            x: centerX,
            y: centerY
        });

        // Otherwise, zoom to the next level
        const nextZoomLevel = zoomLevels[currentIndex + 1];

        // Calculate the new viewport position to keep the center position fixed
        const zoomRatio = nextZoomLevel / currentZoom;
        const newX = flowPosition.x - (flowPosition.x - x) * zoomRatio;
        const newY = flowPosition.y - (flowPosition.y - y) * zoomRatio;

        // Apply the new viewport
        reactFlowInstance.setViewport(
            { x: newX, y: newY, zoom: nextZoomLevel },
            { duration: 400 } // Faster animation
        );

        // Update our local state
        setZoom(nextZoomLevel);
    }, [reactFlowInstance, getClosestZoomLevel]);

    // Function to zoom to the previous predefined level
    const zoomOut = useCallback(() => {
        // Get current viewport
        const { zoom: currentZoom, x, y } = reactFlowInstance.getViewport();

        // Get all zoom levels and sort them
        const zoomLevels = Object.values(ZOOM_LEVELS).sort((a, b) => a - b);

        // Find the closest current level
        const closestCurrentLevel = getClosestZoomLevel(currentZoom);
        const currentIndex = zoomLevels.indexOf(closestCurrentLevel);

        // If we're already at the lowest zoom level, do nothing
        if (currentIndex === 0) {
            return;
        }

        // Otherwise, zoom to the previous level
        const prevZoomLevel = zoomLevels[currentIndex - 1];

        // When zooming out, we don't consider the cursor position
        // But we need to maintain the current viewport position (x, y)
        // to prevent jumping
        reactFlowInstance.setViewport(
            { zoom: prevZoomLevel, x, y },
            { duration: 400 } // Faster animation
        );

        // Update our local state
        setZoom(prevZoomLevel);
    }, [reactFlowInstance, getClosestZoomLevel]);

    // Function to zoom to a specific level with animation
    const zoomTo = useCallback(
        (targetZoom: number, screenPosition?: { x: number; y: number }) => {
            // Get current viewport
            const { zoom: currentZoom, x, y } = reactFlowInstance.getViewport();

            // If no position is provided, use the center of the viewport
            let flowPosition;
            if (screenPosition) {
                // Convert screen coordinates to flow coordinates
                flowPosition = reactFlowInstance.screenToFlowPosition(screenPosition);
            } else {
                // Get the center of the viewport
                const flowElement = document.querySelector('.react-flow');
                if (flowElement) {
                    const rect = flowElement.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;
                    flowPosition = reactFlowInstance.screenToFlowPosition({
                        x: centerX,
                        y: centerY
                    });
                } else {
                    // If we can't get the center, just use the current viewport
                    reactFlowInstance.setViewport({ x, y, zoom: targetZoom }, { duration: 400 });
                    setZoom(targetZoom);
                    return;
                }
            }

            // Calculate the new viewport position to keep the center position fixed
            const zoomRatio = targetZoom / currentZoom;
            const newX = flowPosition.x - (flowPosition.x - x) * zoomRatio;
            const newY = flowPosition.y - (flowPosition.y - y) * zoomRatio;

            // Apply the new viewport
            reactFlowInstance.setViewport(
                { x: newX, y: newY, zoom: targetZoom },
                { duration: 400 } // Faster animation
            );

            // Update our local state
            setZoom(targetZoom);
        },
        [reactFlowInstance]
    );

    // Sync zoom state with ReactFlow's viewport
    useEffect(() => {
        // Use a ref to track if we're in an update cycle to prevent infinite loops
        let isUpdating = false;

        // Create a MutationObserver to watch for viewport changes
        const observer = new MutationObserver(() => {
            if (isUpdating) return;

            isUpdating = true;
            const { zoom: newZoom } = reactFlowInstance.getViewport();

            // Only update if the zoom has actually changed by a significant amount
            if (Math.abs(newZoom - zoom) > 0.01) {
                setZoom(newZoom);
            }

            isUpdating = false;
        });

        // Get the ReactFlow container
        const container = document.querySelector('.react-flow');
        if (container) {
            // Observe changes to the container's style attribute (which includes transform)
            observer.observe(container, { attributes: true, attributeFilter: ['style'] });
        }

        // Clean up observer on unmount
        return () => {
            observer.disconnect();
        };
    }, [reactFlowInstance]); // Remove zoom from dependencies to prevent infinite loops

    // Initialize zoom to the closest predefined level
    useEffect(() => {
        // Only run this once on mount to set the initial zoom level
        // Get the current viewport zoom level
        const { zoom: currentZoom } = reactFlowInstance.getViewport();

        // Find the closest predefined zoom level
        const closestZoomLevel = getClosestZoomLevel(currentZoom);

        // Set initial zoom to the closest predefined level
        setZoom(closestZoomLevel);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Empty dependency array to run only once on mount

    return {
        zoom,
        zoomIn,
        zoomOut,
        zoomTo,
        handleWheel,
        getClosestZoomLevel
    };
}
