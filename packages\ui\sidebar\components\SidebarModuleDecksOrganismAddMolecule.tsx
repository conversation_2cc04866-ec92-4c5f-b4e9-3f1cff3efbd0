import { triplit, handleAddDeck, handleAddDocument, useAuth } from '@repo/db';
import { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router';
import {
    Input,
    Button,
    Calendar,
    Label,
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuSeparator,
    DropdownMenuSub,
    DropdownMenuSubTrigger,
    DropdownMenuSubContent,
    DropdownMenuPortal,
    cn
} from '../../export';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { TbCardsFilled, TbFolderPlus, TbFilePlus, TbCards, TbUpload } from 'react-icons/tb';
import { useDropzone } from 'react-dropzone';
import { SidebarModuleDecksOrganismAddMoleculeDeckAtom } from './SidebarModuleDecksOrganismAddMoleculeDeckAtom';

export const SidebarModuleDecksOrganismAddMolecule = () => {
    // State for dropdown menu
    const [open, setOpen] = useState(false);

    // State for folder creation
    const [folderName, setFolderName] = useState('');
    const [isSubmittingFolder, setIsSubmittingFolder] = useState(false);

    // State for document upload
    const [files, setFiles] = useState<File[]>([]);
    const [isSubmittingDocument, setIsSubmittingDocument] = useState(false);

    // Get current user
    const { user } = useAuth();

    // Handle deck creation

    // Handle folder creation
    const handleFolderSubmit = async (e: React.MouseEvent) => {
        e.preventDefault();

        if (!folderName.trim() || !user) return;

        setIsSubmittingFolder(true);
        try {
            // Create a new folder in Triplit
            const newFolder = {
                id: crypto.randomUUID(),
                name: folderName.trim(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                user_id: user.id // Use the actual user ID
            };

            await triplit.insert('folders', newFolder);

            // Reset form
            setFolderName('');
            setOpen(false);
        } catch (error) {
            console.error('Error creating folder:', error);
        } finally {
            setIsSubmittingFolder(false);
        }
    };

    // Setup dropzone for document upload
    const onDrop = useCallback((acceptedFiles: File[]) => {
        setFiles(acceptedFiles);
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'application/pdf': ['.pdf'],
            'text/plain': ['.txt'],
            'text/markdown': ['.md'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
        }
    });

    // Handle document upload
    const handleDocumentUpload = async (e: React.MouseEvent) => {
        e.preventDefault();

        if (files.length === 0 || !user) return;

        setIsSubmittingDocument(true);
        try {
            // Process document upload for each file using the utility function
            for (const file of files) {
                const result = await handleAddDocument({ file, user });
                if (result) {
                    console.log('Document added successfully:', result);
                } else {
                    console.error('Failed to add document');
                }
            }

            // Reset form
            setFiles([]);
            setOpen(false);
        } catch (error) {
            console.error('Error uploading documents:', error);
        } finally {
            setIsSubmittingDocument(false);
        }
    };

    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger className='text-gray-500 hover:text-gray-700'>
                +
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end' className='w-56'>
                {/* Add Deck Option */}
                <DropdownMenuSub>
                    <DropdownMenuSubTrigger className='cursor-pointer'>
                        <TbCards className='mr-2 h-4 w-4' />
                        <span>Add Deck</span>
                    </DropdownMenuSubTrigger>
                    <SidebarModuleDecksOrganismAddMoleculeDeckAtom />
                </DropdownMenuSub>

                {/* Add Folder Option */}
                <DropdownMenuSub>
                    <DropdownMenuSubTrigger className='cursor-pointer'>
                        <TbFolderPlus className='mr-2 h-4 w-4' />
                        <span>Add Folder</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuPortal>
                        <DropdownMenuSubContent className='w-72 p-2'>
                            <div className='space-y-3 px-1 py-2'>
                                <h3 className='text-sm font-medium'>Add New Folder</h3>
                                <div className='space-y-1'>
                                    <Label htmlFor='folder-name' className='text-xs'>
                                        Name
                                    </Label>
                                    <Input
                                        id='folder-name'
                                        placeholder='Enter folder name'
                                        value={folderName}
                                        onChange={e => setFolderName(e.target.value)}
                                        className='h-8 text-sm'
                                    />
                                </div>

                                <div className='flex justify-end space-x-2 pt-2'>
                                    <Button
                                        variant='outline'
                                        size='sm'
                                        onClick={() => {
                                            setFolderName('');
                                            setOpen(false);
                                        }}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        size='sm'
                                        onClick={handleFolderSubmit}
                                        disabled={!folderName.trim() || isSubmittingFolder}
                                    >
                                        {isSubmittingFolder ? 'Creating...' : 'Create Folder'}
                                    </Button>
                                </div>
                            </div>
                        </DropdownMenuSubContent>
                    </DropdownMenuPortal>
                </DropdownMenuSub>

                <DropdownMenuSeparator />

                {/* Add Document Option */}
                <DropdownMenuSub>
                    <DropdownMenuSubTrigger className='cursor-pointer'>
                        <TbFilePlus className='mr-2 h-4 w-4' />
                        <span>Add Document</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuPortal>
                        <DropdownMenuSubContent className='w-80 p-2'>
                            <div className='space-y-3 px-1 py-2'>
                                <h3 className='text-sm font-medium'>Upload Document</h3>
                                <div
                                    {...getRootProps()}
                                    className={cn(
                                        'flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-4 transition-colors',
                                        isDragActive && 'border-blue-500 bg-blue-50',
                                        isSubmittingDocument && 'cursor-not-allowed opacity-50'
                                    )}
                                >
                                    <input {...getInputProps()} disabled={isSubmittingDocument} />
                                    <div className='flex flex-col items-center justify-center space-y-2 text-center'>
                                        <TbUpload className='h-8 w-8 text-gray-400' />
                                        <p className='text-xs font-medium text-gray-700'>
                                            {isDragActive
                                                ? 'Drop the files here...'
                                                : 'Drag & drop files here, or click to select files'}
                                        </p>
                                        <p className='text-xs text-gray-500'>
                                            PDF, TXT, MD, DOC, DOCX up to 10MB
                                        </p>
                                    </div>
                                </div>

                                {files.length > 0 && (
                                    <div className='mt-2 space-y-1'>
                                        <p className='text-xs font-medium text-gray-700'>
                                            Selected files:
                                        </p>
                                        <ul className='max-h-32 overflow-y-auto rounded-md border border-gray-200 bg-gray-50 p-1'>
                                            {files.map((file, index) => (
                                                <li
                                                    key={index}
                                                    className='flex items-center space-x-2 rounded-md p-1 text-xs text-gray-700'
                                                >
                                                    <TbFilePlus className='h-3 w-3 text-gray-500' />
                                                    <span className='flex-1 truncate'>
                                                        {file.name}
                                                    </span>
                                                    <span className='text-xs text-gray-500'>
                                                        {(file.size / 1024 / 1024).toFixed(2)} MB
                                                    </span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                <div className='flex justify-end space-x-2 pt-2'>
                                    <Button
                                        variant='outline'
                                        size='sm'
                                        onClick={() => {
                                            setFiles([]);
                                            setOpen(false);
                                        }}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        size='sm'
                                        onClick={handleDocumentUpload}
                                        disabled={files.length === 0 || isSubmittingDocument}
                                    >
                                        {isSubmittingDocument ? 'Uploading...' : 'Upload Document'}
                                    </Button>
                                </div>
                            </div>
                        </DropdownMenuSubContent>
                    </DropdownMenuPortal>
                </DropdownMenuSub>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};
