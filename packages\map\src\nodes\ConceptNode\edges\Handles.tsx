import React, { useEffect } from 'react';
import { Handle, Position, useUpdateNodeInternals, useNodeId } from '@xyflow/react';

export const NodeHandles = () => {
    const nodeId = useNodeId();
    const updateNodeInternals = useUpdateNodeInternals();

    useEffect(() => {
        if (nodeId) {
            console.log(`[NodeHandles] Updating node internals for node ${nodeId}`);
            updateNodeInternals(nodeId);
        }
    }, [nodeId, updateNodeInternals]);

    const handleStyle = {
        opacity: 0.0,
        backgroundColor: '#ddd',
        border: '1px solid #ccc',
        zIndex: 1
    };

    return (
        <>
            <Handle
                type='source'
                position={Position.Top}
                id='top-source'
                className='!-top-1 !h-2 !min-h-0 !w-4 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />
            <Handle
                type='target'
                position={Position.Top}
                id='top-target'
                className='!-top-1 !h-2 !min-h-0 !w-4 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />

            <Handle
                type='source'
                position={Position.Bottom}
                id='bottom-source'
                className='!-bottom-1 !h-2 !min-h-0 !w-4 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />
            <Handle
                type='target'
                position={Position.Bottom}
                id='bottom-target'
                className='!-bottom-1 !h-2 !min-h-0 !w-4 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />

            <Handle
                type='source'
                position={Position.Left}
                id='left-source'
                className='!-left-1 !h-4 !min-h-0 !w-2 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />
            <Handle
                type='target'
                position={Position.Left}
                id='left-target'
                className='!-left-1 !h-4 !min-h-0 !w-2 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />

            <Handle
                type='source'
                position={Position.Right}
                id='right-source'
                className='!-right-1 !h-4 !min-h-0 !w-2 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />
            <Handle
                type='target'
                position={Position.Right}
                id='right-target'
                className='!-right-1 !h-4 !min-h-0 !w-2 !min-w-0 hover:!opacity-100'
                style={handleStyle}
            />
        </>
    );
};
