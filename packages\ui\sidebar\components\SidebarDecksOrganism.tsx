import { triplit, useAuth } from '@repo/db';
import { useQuery } from '@triplit/react';
import { Link } from 'react-router';
import {
    TbCardsFilled,
    TbDotsVertical,
    TbFolder,
    TbFolderOpen,
    TbChevronRight,
    TbChevronDown,
    TbFile
} from 'react-icons/tb';
import { SidebarModuleDecksOrganismAddMolecule } from './SidebarModuleDecksOrganismAddMolecule';
import { SidebarItemDropdownMenu } from './SidebarItemDropdownMenu';
import { useState, useCallback, useEffect, useRef } from 'react';
import { cn } from '../../export';

export function SidebarDecksOrganism() {
    const { results: decks } = useQuery(triplit, triplit.query('decks'));
    const { results: folders } = useQuery(triplit, triplit.query('folders'));
    const { results: documents } = useQuery(triplit, triplit.query('documents'));

    const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
    const [draggedOverFolderId, setDraggedOverFolderId] = useState<string | null>(null);
    const [isDraggingItem, setIsDraggingItem] = useState(false);
    // We track the type of item being dragged to handle it correctly in the drop handler
    const [draggedItemType, setDraggedItemType] = useState<'deck' | 'document' | null>(null);
    const expandFolderTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const { user } = useAuth();

    // Initialize expanded state for folders
    useEffect(() => {
        if (folders) {
            const initialExpandedState: Record<string, boolean> = {};
            folders.forEach(folder => {
                initialExpandedState[folder.id] = true; // Default to expanded
            });
            setExpandedFolders(initialExpandedState);
        }
    }, [folders]);

    // Clean up any timeouts when component unmounts
    useEffect(() => {
        return () => {
            if (expandFolderTimeoutRef.current) {
                clearTimeout(expandFolderTimeoutRef.current);
            }
        };
    }, []);

    const toggleFolder = useCallback((folderId: string) => {
        setExpandedFolders(prev => ({
            ...prev,
            [folderId]: !prev[folderId]
        }));
    }, []);

    const expandFolder = useCallback((folderId: string) => {
        setExpandedFolders(prev => ({
            ...prev,
            [folderId]: true
        }));
    }, []);

    const handleDragStart = useCallback(
        (e: React.DragEvent, itemId: string, type: 'deck' | 'document') => {
            e.dataTransfer.setData('text/plain', itemId);
            e.dataTransfer.setData('itemType', type);
            e.dataTransfer.effectAllowed = 'move';
            setIsDraggingItem(true);
            setDraggedItemType(type);
        },
        []
    );

    const handleDragEnd = useCallback(() => {
        setIsDraggingItem(false);
        setDraggedItemType(null);
        setDraggedOverFolderId(null);

        if (expandFolderTimeoutRef.current) {
            clearTimeout(expandFolderTimeoutRef.current);
            expandFolderTimeoutRef.current = null;
        }
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    }, []);

    const handleFolderDragOver = useCallback(
        (e: React.DragEvent, folderId: string) => {
            e.preventDefault();
            e.stopPropagation(); // Prevent bubbling to parent elements
            e.dataTransfer.dropEffect = 'move';

            setDraggedOverFolderId(folderId);

            // Set a timeout to expand the folder if hovering for a short time
            if (!expandedFolders[folderId] && !expandFolderTimeoutRef.current) {
                expandFolderTimeoutRef.current = setTimeout(() => {
                    expandFolder(folderId);
                    expandFolderTimeoutRef.current = null;
                }, 600); // Expand after 600ms of hovering
            }
        },
        [expandedFolders, expandFolder]
    );

    const handleFolderDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();

        // Only clear if we're leaving the folder (not entering a child element)
        if (!e.currentTarget.contains(e.relatedTarget as Node)) {
            setDraggedOverFolderId(null);

            if (expandFolderTimeoutRef.current) {
                clearTimeout(expandFolderTimeoutRef.current);
                expandFolderTimeoutRef.current = null;
            }
        }
    }, []);

    const handleDrop = useCallback(
        async (e: React.DragEvent, folderId: string | null) => {
            e.preventDefault();
            e.stopPropagation(); // Prevent bubbling to parent elements

            const itemId = e.dataTransfer.getData('text/plain');
            const itemType = e.dataTransfer.getData('itemType') as 'deck' | 'document';

            // Reset drag state
            setDraggedOverFolderId(null);
            setIsDraggingItem(false);
            setDraggedItemType(null);

            if (expandFolderTimeoutRef.current) {
                clearTimeout(expandFolderTimeoutRef.current);
                expandFolderTimeoutRef.current = null;
            }

            if (!itemId || !user) return;

            try {
                if (itemType === 'deck') {
                    // Update the deck's folder_id
                    await triplit.update('decks', itemId, {
                        folder_id: folderId,
                        updated_at: new Date().toISOString()
                    });
                } else if (itemType === 'document') {
                    // Update the document's folder_id
                    await triplit.update('documents', itemId, {
                        folder_id: folderId,
                        updated_at: new Date().toISOString()
                    });
                }
            } catch (error) {
                console.error(`Error moving ${itemType} to folder:`, error);
            }
        },
        [user]
    );

    // Group decks and documents by folder
    const decksInRoot = decks?.filter(deck => !deck.folder_id) || [];
    const documentsInRoot = documents?.filter(doc => !doc.folder_id) || [];
    const decksInFolders: Record<string, typeof decks> = {};
    const documentsInFolders: Record<string, typeof documents> = {};

    folders?.forEach(folder => {
        decksInFolders[folder.id] = decks?.filter(deck => deck.folder_id === folder.id) || [];
        documentsInFolders[folder.id] = documents?.filter(doc => doc.folder_id === folder.id) || [];
    });

    return (
        <div className='grid gap-1'>
            <div className='flex h-7 items-center justify-between px-3'>
                <h3 className='text-[11px] font-semibold uppercase tracking-wider text-gray-500'>
                    Library
                </h3>
                <SidebarModuleDecksOrganismAddMolecule />
            </div>
            <div
                className={cn('space-y-1 p-1', isDraggingItem && 'rounded-sm bg-gray-50/50')}
                onDragOver={handleDragOver}
                onDrop={e => handleDrop(e, null)} // Drop in root = null folder_id
            >
                {/* Render decks without a folder (root level) */}
                {decksInRoot.map(deck => (
                    <SidebarDecksItemMolecule
                        key={`deck-${deck.id}`}
                        deckId={deck.id}
                        deckName={deck.name}
                        onDragStart={e => handleDragStart(e, deck.id, 'deck')}
                        onDragEnd={handleDragEnd}
                    />
                ))}

                {/* Render documents without a folder (root level) */}
                {documentsInRoot.map(doc => (
                    <SidebarDocumentItemMolecule
                        key={`doc-${doc.id}`}
                        documentId={doc.id}
                        documentName={doc.name}
                        documentType={doc.type}
                        onDragStart={e => handleDragStart(e, doc.id, 'document')}
                        onDragEnd={handleDragEnd}
                    />
                ))}

                {/* Render folders and their items */}
                {folders?.map(folder => (
                    <SidebarFolderItemMolecule
                        key={folder.id}
                        folderId={folder.id}
                        folderName={folder.name}
                        decks={decksInFolders[folder.id] || []}
                        documents={documentsInFolders[folder.id] || []}
                        isExpanded={expandedFolders[folder.id] || false}
                        isDraggedOver={draggedOverFolderId === folder.id}
                        onToggle={() => toggleFolder(folder.id)}
                        onDragOver={handleFolderDragOver}
                        onDragLeave={handleFolderDragLeave}
                        onDrop={handleDrop}
                        onDragStart={handleDragStart}
                        onDragEnd={handleDragEnd}
                    />
                ))}
            </div>
        </div>
    );
}

type SidebarDecksItemMoleculeProps = {
    deckId: string;
    deckName: string;
    onDragStart: (e: React.DragEvent) => void;
    onDragEnd: () => void;
};

const SidebarDecksItemMolecule = ({
    deckId,
    deckName,
    onDragStart,
    onDragEnd
}: SidebarDecksItemMoleculeProps) => {
    return (
        <Link
            to={`/app/maps/${deckId}`}
            key={deckId}
            className='group flex h-6 items-center justify-between rounded-sm hover:bg-gray-50'
            draggable
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
        >
            <div className='flex items-center'>
                <TbCardsFilled className='mr-2 h-4 w-4 text-gray-300 group-hover:text-gray-400' />
                <span className='text-xs font-normal text-gray-400 group-hover:text-gray-500'>
                    {deckName}
                </span>
            </div>
            <div>
                <SidebarItemDropdownMenu itemId={deckId} itemName={deckName} itemType='deck' />
            </div>
        </Link>
    );
};

type SidebarDocumentItemMoleculeProps = {
    documentId: string;
    documentName: string;
    documentType: string;
    onDragStart: (e: React.DragEvent) => void;
    onDragEnd: () => void;
};

const SidebarDocumentItemMolecule = ({
    documentId,
    documentName,
    documentType,
    onDragStart,
    onDragEnd
}: SidebarDocumentItemMoleculeProps) => {
    return (
        <div
            className='group flex h-6 cursor-pointer items-center justify-between rounded-sm hover:bg-gray-50'
            draggable
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
        >
            <div className='flex items-center'>
                <TbFile className='mr-2 h-4 w-4 text-gray-300 group-hover:text-gray-400' />
                <span className='text-xs font-normal text-gray-400 group-hover:text-gray-500'>
                    {documentName}
                </span>
            </div>
            <div>
                <SidebarItemDropdownMenu
                    itemId={documentId}
                    itemName={documentName}
                    itemType='document'
                />
            </div>
        </div>
    );
};

type SidebarFolderItemMoleculeProps = {
    folderId: string;
    folderName: string;
    decks: Array<{ id: string; name: string }>;
    documents: Array<{ id: string; name: string; type: string }>;
    isExpanded: boolean;
    isDraggedOver: boolean;
    onToggle: () => void;
    onDragOver: (e: React.DragEvent, folderId: string) => void;
    onDragLeave: (e: React.DragEvent) => void;
    onDrop: (e: React.DragEvent, folderId: string | null) => void;
    onDragStart: (e: React.DragEvent, itemId: string, type: 'deck' | 'document') => void;
    onDragEnd: () => void;
};

const SidebarFolderItemMolecule = ({
    folderId,
    folderName,
    decks,
    documents,
    isExpanded,
    isDraggedOver,
    onToggle,
    onDragOver,
    onDragLeave,
    onDrop,
    onDragStart,
    onDragEnd
}: SidebarFolderItemMoleculeProps) => {
    return (
        <div
            className={cn('mb-1 rounded-sm', isDraggedOver && 'bg-blue-50/50 ring-1 ring-blue-200')}
            onDragOver={e => onDragOver(e, folderId)}
            onDragLeave={onDragLeave}
            onDrop={e => onDrop(e, folderId)}
        >
            <div className='group flex h-6 cursor-pointer items-center justify-between rounded-sm hover:bg-gray-50'>
                <div className='flex items-center' onClick={onToggle}>
                    {isExpanded ? (
                        <TbChevronDown className='mr-1 h-3 w-3 text-gray-400' />
                    ) : (
                        <TbChevronRight className='mr-1 h-3 w-3 text-gray-400' />
                    )}
                    {isExpanded ? (
                        <TbFolderOpen
                            className={cn(
                                'mr-2 h-4 w-4 text-gray-300 group-hover:text-gray-400',
                                isDraggedOver && 'text-blue-400'
                            )}
                        />
                    ) : (
                        <TbFolder
                            className={cn(
                                'mr-2 h-4 w-4 text-gray-300 group-hover:text-gray-400',
                                isDraggedOver && 'text-blue-400'
                            )}
                        />
                    )}
                    <span className='text-xs font-medium text-gray-500 group-hover:text-gray-600'>
                        {folderName}
                    </span>
                </div>
                <div onClick={e => e.stopPropagation()}>
                    <SidebarItemDropdownMenu
                        itemId={folderId}
                        itemName={folderName}
                        itemType='folder'
                    />
                </div>
            </div>

            {/* Render items inside this folder when expanded */}
            {isExpanded && (
                <div className='ml-5 mt-1 space-y-1'>
                    {decks.length > 0 || documents.length > 0 ? (
                        <>
                            {/* Render decks */}
                            {decks.map(deck => (
                                <SidebarDecksItemMolecule
                                    key={`deck-${deck.id}`}
                                    deckId={deck.id}
                                    deckName={deck.name}
                                    onDragStart={e => onDragStart(e, deck.id, 'deck')}
                                    onDragEnd={onDragEnd}
                                />
                            ))}

                            {/* Render documents */}
                            {documents.map(doc => (
                                <SidebarDocumentItemMolecule
                                    key={`doc-${doc.id}`}
                                    documentId={doc.id}
                                    documentName={doc.name}
                                    documentType={doc.type}
                                    onDragStart={e => onDragStart(e, doc.id, 'document')}
                                    onDragEnd={onDragEnd}
                                />
                            ))}
                        </>
                    ) : (
                        <div className='flex h-6 items-center px-2'>
                            <span className='text-xs italic text-gray-400'>Empty folder</span>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};
