import { useState } from 'react';
import {
    Input,
    Button,
    Calendar,
    Label,
    DropdownMenuPortal,
    DropdownMenuSubContent,
    Textarea,
    Command,
    CommandInput,
    CommandList,
    CommandEmpty,
    CommandGroup,
    CommandItem,
    Popover,
    PopoverContent,
    PopoverTrigger
} from '../../export';
import { format } from 'date-fns';
import { TbCheck, TbChevronDown } from 'react-icons/tb';
import { handleAddDeck, useAuth } from '@repo/db';

// Popular languages with their codes and names
const languages = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish (Español)' },
    { value: 'fr', label: 'French (Français)' },
    { value: 'de', label: 'German (Deutsch)' },
    { value: 'it', label: 'Italian (Italiano)' },
    { value: 'pt', label: 'Portuguese (Português)' },
    { value: 'ru', label: 'Russian (Русский)' },
    { value: 'zh', label: 'Chinese (中文)' },
    { value: 'ja', label: 'Japanese (日本語)' },
    { value: 'ko', label: 'Korean (한국어)' },
    { value: 'ar', label: 'Arabic (العربية)' },
    { value: 'hi', label: 'Hindi (हिन्दी)' },
    { value: 'bn', label: 'Bengali (বাংলা)' },
    { value: 'nl', label: 'Dutch (Nederlands)' },
    { value: 'sv', label: 'Swedish (Svenska)' },
    { value: 'no', label: 'Norwegian (Norsk)' },
    { value: 'da', label: 'Danish (Dansk)' },
    { value: 'fi', label: 'Finnish (Suomi)' },
    { value: 'pl', label: 'Polish (Polski)' },
    { value: 'tr', label: 'Turkish (Türkçe)' },
    { value: 'cs', label: 'Czech (Čeština)' },
    { value: 'hu', label: 'Hungarian (Magyar)' },
    { value: 'th', label: 'Thai (ไทย)' },
    { value: 'vi', label: 'Vietnamese (Tiếng Việt)' },
    { value: 'uk', label: 'Ukrainian (Українська)' },
    { value: 'el', label: 'Greek (Ελληνικά)' },
    { value: 'he', label: 'Hebrew (עברית)' },
    { value: 'id', label: 'Indonesian (Bahasa Indonesia)' },
    { value: 'ms', label: 'Malay (Bahasa Melayu)' },
    { value: 'ro', label: 'Romanian (Română)' }
];

export const SidebarModuleDecksOrganismAddMoleculeDeckAtom = () => {
    const [deckName, setDeckName] = useState('');
    const [learningOutcome, setLearningOutcome] = useState('');
    const [deckDeadline, setDeckDeadline] = useState<Date | undefined>(undefined);
    const [language, setLanguage] = useState('en');
    const [openLanguagePopover, setOpenLanguagePopover] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const { user } = useAuth();

    return (
        <>
            <DropdownMenuPortal>
                <DropdownMenuSubContent className='w-72 p-2'>
                    <div className='space-y-3 px-1 py-2'>
                        <h3 className='text-sm font-medium'>Add New Deck</h3>
                        <div className='space-y-2'>
                            <div className='space-y-1'>
                                <Label htmlFor='deck-name' className='text-xs'>
                                    Name
                                </Label>
                                <Input
                                    id='deck-name'
                                    placeholder='Enter deck name'
                                    value={deckName}
                                    onChange={e => setDeckName(e.target.value)}
                                    className='h-8 text-sm'
                                />
                            </div>

                            <div className='space-y-1'>
                                <Label htmlFor='learning-outcome' className='text-xs'>
                                    Learning Outcome
                                </Label>
                                <Textarea
                                    id='learning-outcome'
                                    placeholder='What do you want to learn with this deck?'
                                    value={learningOutcome}
                                    onChange={e => setLearningOutcome(e.target.value)}
                                    className='min-h-[60px] text-sm'
                                />
                            </div>

                            <div className='space-y-1'>
                                <Label htmlFor='language' className='text-xs'>
                                    Language
                                </Label>
                                <Popover
                                    open={openLanguagePopover}
                                    onOpenChange={setOpenLanguagePopover}
                                >
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant='outline'
                                            role='combobox'
                                            aria-expanded={openLanguagePopover}
                                            className='h-8 w-full justify-between text-sm'
                                        >
                                            {language
                                                ? languages.find(lang => lang.value === language)
                                                      ?.label
                                                : 'Select language...'}
                                            <TbChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className='w-full p-0'>
                                        <Command>
                                            <CommandInput
                                                placeholder='Search language...'
                                                value={searchQuery}
                                                onValueChange={setSearchQuery}
                                                className='h-9'
                                            />
                                            <CommandList>
                                                <CommandEmpty>No language found.</CommandEmpty>
                                                <CommandGroup>
                                                    {languages.map(lang => (
                                                        <CommandItem
                                                            key={lang.value}
                                                            value={lang.value}
                                                            onSelect={currentValue => {
                                                                setLanguage(currentValue);
                                                                setOpenLanguagePopover(false);
                                                                setSearchQuery('');
                                                            }}
                                                        >
                                                            {lang.label}
                                                            {language === lang.value && (
                                                                <TbCheck className='ml-auto h-4 w-4' />
                                                            )}
                                                        </CommandItem>
                                                    ))}
                                                </CommandGroup>
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                            </div>

                            <div className='space-y-1'>
                                <Label htmlFor='deck-deadline' className='text-xs'>
                                    Deadline (optional)
                                </Label>
                                <Calendar
                                    mode='single'
                                    selected={deckDeadline}
                                    onSelect={setDeckDeadline}
                                    className='rounded-md border'
                                    disabled={date => date < new Date()}
                                />
                                {deckDeadline && (
                                    <p className='text-xs text-gray-500'>
                                        Selected: {format(deckDeadline, 'PPP')}
                                    </p>
                                )}
                            </div>
                        </div>

                        <div className='flex justify-end space-x-2 pt-2'>
                            <Button
                                variant='outline'
                                size='sm'
                                onClick={() => {
                                    setDeckName('');
                                    setDeckDeadline(undefined);
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                size='sm'
                                onClick={e => {
                                    handleAddDeck({
                                        deckName,
                                        learningOutcome,
                                        deckDeadline,
                                        language,
                                        e,
                                        user
                                    }).then(() => {
                                        setDeckName('');
                                        setLearningOutcome('');
                                        setDeckDeadline(undefined);
                                        setLanguage('en');
                                        setSearchQuery('');
                                    });
                                }}
                                disabled={!deckName.trim()}
                            >
                                Add
                            </Button>
                        </div>
                    </div>
                </DropdownMenuSubContent>
            </DropdownMenuPortal>
        </>
    );
};
