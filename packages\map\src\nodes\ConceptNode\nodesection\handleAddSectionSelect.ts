import { NODE_SECTIONS } from '@repo/utils';
import { handleAddSection } from './handleAddSection';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';
import { useApiStore, NodeSection, NodeTag } from '@repo/api';

export const handleAddSectionSelect = async (
    sectionKey: string,
    conceptTitle: string,
    sections: Record<string, SectionConfig>,
    tags: { name: string; type: string }[],
    nodeId: string,
    startEditingSectionLabel: (sectionId: string, label: string) => void,
    generateSectionContentWithLLM: (section: SectionConfig) => Promise<void>,
    updateNodeData: (data: Record<string, any>) => void,
    setSectionSelectPopoverOpen: (open: boolean) => void
) => {
    // Handle custom section creation
    if (sectionKey === 'custom') {
        const customLabel = 'Custom Section';
        const newSection = await handleAddSection(
            startEditingSectionLabel,
            generateSectionContentWithLLM,
            { label: customLabel, icon: '📝' }
        );

        const updatedSections = {
            ...sections,
            [newSection.id]: newSection
        };

        updateNodeData({
            sections: updatedSections
        });

        setSectionSelectPopoverOpen(false);
        return;
    }

    const sectionDefaults = NODE_SECTIONS[sectionKey as keyof typeof NODE_SECTIONS];
    if (sectionDefaults) {
        // Core Idea doesn't get a descriptive suffix
        if (sectionKey === 'coreIdea') {
            const newSection = await handleAddSection(
                startEditingSectionLabel,
                generateSectionContentWithLLM,
                { label: sectionDefaults.defaultTitle, icon: sectionDefaults.icon }
            );

            const updatedSections = {
                ...sections,
                [newSection.id]: newSection
            };

            updateNodeData({
                sections: updatedSections
            });

            // Generate content for the section after adding it
            generateSectionContentWithLLM(newSection);

            setSectionSelectPopoverOpen(false);
            return;
        }

        // For all other section types, generate a descriptive title
        try {
            // We don't need to set loading state for the button anymore
            // setLoadingTitle(true);

            // Convert sections to the format expected by the API
            const apiSections: Record<string, NodeSection> = {};
            Object.entries(sections).forEach(([key, section]) => {
                apiSections[key] = {
                    id: section.id,
                    label: section.label,
                    icon: section.icon as string,
                    value: section.value,
                    collapsible: section.collapsible
                };
            });

            // Convert tags to the format expected by the API
            const apiTags: NodeTag[] = tags.map(tag => ({
                name: tag.name,
                type: tag.type
            }));

            // Generate a descriptive title using the API store
            const apiStore = useApiStore.getState();
            apiStore.setSourceConceptTitle(conceptTitle);
            apiStore.setSourceSections(apiSections);
            apiStore.setRequestType('section');
            apiStore.setSectionType(sectionDefaults.defaultTitle);
            apiStore.setCurrentNodeId(nodeId);

            // Generate the section content
            await apiStore.generate();

            // Get the generated content and heading
            const generatedContent = apiStore.generatedSection;
            const generatedHeading = apiStore.generatedSectionHeading;
            let generatedTitle = generatedHeading || sectionDefaults.defaultTitle;

            // Ensure the title is not too long
            if (generatedTitle.length > 50) {
                generatedTitle = `${generatedTitle.substring(0, 47)}...`;
            }

            // Add the section with the generated title
            const newSection = await handleAddSection(
                startEditingSectionLabel,
                generateSectionContentWithLLM,
                { label: generatedTitle, icon: sectionDefaults.icon }
            );

            const updatedSections = {
                ...sections,
                [newSection.id]: newSection
            };

            updateNodeData({
                sections: updatedSections
            });

            // Generate content for the section after adding it
            generateSectionContentWithLLM(newSection);
        } catch (error) {
            console.error('Error generating section title:', error);
            // Count existing sections of this type for fallback
            const existingSectionsOfType = Object.values(sections || {}).filter(section =>
                section.label.startsWith(sectionDefaults.defaultTitle)
            );

            // Fallback to numbered suffix or just the default title
            let newSection;
            if (existingSectionsOfType.length > 0) {
                const suffix = ` - ${existingSectionsOfType.length + 1}`;
                newSection = await handleAddSection(
                    startEditingSectionLabel,
                    generateSectionContentWithLLM,
                    {
                        label: `${sectionDefaults.defaultTitle}${suffix}`,
                        icon: sectionDefaults.icon
                    }
                );
            } else {
                // First instance fallback
                newSection = await handleAddSection(
                    startEditingSectionLabel,
                    generateSectionContentWithLLM,
                    { label: sectionDefaults.defaultTitle, icon: sectionDefaults.icon }
                );
            }

            const updatedSections = {
                ...sections,
                [newSection.id]: newSection
            };

            updateNodeData({
                sections: updatedSections
            });

            // Generate content for the section after adding it
            generateSectionContentWithLLM(newSection);
        } finally {
            // We don't need to clear loading state for the button anymore
            // setLoadingTitle(false);
        }
    }
    setSectionSelectPopoverOpen(false); // Close popover after selection
};
