{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "allowImportingTsExtensions": true, "isolatedModules": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "noEmit": true, "strictNullChecks": true, "allowJs": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "useDefineForClassFields": true, "sourceMap": true}, "include": ["src", "eslintrc.config.js"], "exclude": ["node_modules", "public", "build", "dist"]}