import { NodeSection } from '../openai';

/**
 * Formats existing section labels from a concept
 */
export const formatSectionLabels = (sourceSections: Record<string, NodeSection>): string => {
    return Object.values(sourceSections)
        .map(section => section.label)
        .join(', ');
};

/**
 * Creates language instruction based on the specified language
 */
export const getLanguageInstruction = (language: string = 'en'): string => {
    return language === 'en' || language === 'english'
        ? 'Use plain, simple explanatory language that is very high yielding from a knowledge gaining perspective! Try to use easy english vocabulary.'
        : `Write the content in ${language} language. Use plain, simple explanatory language that is very high yielding from a knowledge gaining perspective!`;
};

/**
 * Creates the expert intro used in all prompts
 */
export const getExpertIntro = () => {
    return `You are an expert educational content creator specializing in creating educational content that is high-yielding from a knowledge perspective.\m`;
};

export const getLearningOutcome = (learningOutcome?: string) => {
    return `${learningOutcome ? `Learning outcome: ${learningOutcome}` : ''}`;
};
