import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { supabase } from '@repo/auth';
import {
    Button,
    Input,
    Card,
    CardHeader,
    CardContent,
    CardTitle,
    CardDescription,
    Form,
    FormField,
    FormItem,
    FormControl,
    FormMessage,
    FormLabel,
    Alert,
    AlertTitle,
    AlertDescription
} from '@repo/ui';
import { Tb<PERSON><PERSON>, TbShield, TbEye, TbEyeOff } from 'react-icons/tb';

// Schema for password change form
const passwordSchema = z
    .object({
        currentPassword: z.string().min(6, { message: 'Password must be at least 6 characters' }),
        newPassword: z
            .string()
            .min(6, { message: 'Password must be at least 6 characters' })
            .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
                message:
                    'Password must contain at least one uppercase letter, one lowercase letter, and one number'
            }),
        confirmPassword: z.string().min(6, { message: 'Password must be at least 6 characters' })
    })
    .refine(data => data.newPassword === data.confirmPassword, {
        message: "Passwords don't match",
        path: ['confirmPassword']
    });

type PasswordFormValues = z.infer<typeof passwordSchema>;

interface SecurityFormProps {
    user: any;
}

export default function SecurityForm({ user }: SecurityFormProps) {
    const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
    const [passwordError, setPasswordError] = useState<string | null>(null);
    const [isPasswordLoading, setIsPasswordLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    // Password form
    const passwordForm = useForm<PasswordFormValues>({
        resolver: zodResolver(passwordSchema),
        defaultValues: {
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
        }
    });

    // Handle password change
    async function onPasswordSubmit(values: PasswordFormValues) {
        setIsPasswordLoading(true);
        setPasswordSuccess(null);
        setPasswordError(null);

        try {
            // First verify the current password by attempting to sign in
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user?.email || '',
                password: values.currentPassword
            });

            if (signInError) {
                throw new Error('Current password is incorrect');
            }

            // Then update the password
            const { error } = await supabase.auth.updateUser({
                password: values.newPassword
            });

            if (error) {
                throw error;
            }

            setPasswordSuccess('Password updated successfully');
            passwordForm.reset({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            });
        } catch (error: any) {
            setPasswordError(error.message || 'Failed to update password');
        } finally {
            setIsPasswordLoading(false);
        }
    }

    return (
        <>
            <Card className='mb-6'>
                <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                        <TbKey className='h-5 w-5 text-primary' />
                        Change Password
                    </CardTitle>
                    <CardDescription>Update your password</CardDescription>
                </CardHeader>
                <CardContent>
                    <Form {...passwordForm}>
                        <form
                            onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                            className='space-y-4'
                        >
                            <FormField
                                control={passwordForm.control}
                                name='currentPassword'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Current Password</FormLabel>
                                        <div className='relative'>
                                            <FormControl>
                                                <Input
                                                    type={showPassword ? 'text' : 'password'}
                                                    placeholder='Current password'
                                                    {...field}
                                                />
                                            </FormControl>
                                            <Button
                                                type='button'
                                                variant='ghost'
                                                size='sm'
                                                className='absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-600'
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <TbEyeOff className='h-4 w-4' />
                                                ) : (
                                                    <TbEye className='h-4 w-4' />
                                                )}
                                            </Button>
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={passwordForm.control}
                                name='newPassword'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>New Password</FormLabel>
                                        <div className='relative'>
                                            <FormControl>
                                                <Input
                                                    type={showPassword ? 'text' : 'password'}
                                                    placeholder='New password'
                                                    {...field}
                                                />
                                            </FormControl>
                                            <Button
                                                type='button'
                                                variant='ghost'
                                                size='sm'
                                                className='absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-600'
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <TbEyeOff className='h-4 w-4' />
                                                ) : (
                                                    <TbEye className='h-4 w-4' />
                                                )}
                                            </Button>
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={passwordForm.control}
                                name='confirmPassword'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Confirm New Password</FormLabel>
                                        <div className='relative'>
                                            <FormControl>
                                                <Input
                                                    type={showPassword ? 'text' : 'password'}
                                                    placeholder='Confirm new password'
                                                    {...field}
                                                />
                                            </FormControl>
                                            <Button
                                                type='button'
                                                variant='ghost'
                                                size='sm'
                                                className='absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-600'
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <TbEyeOff className='h-4 w-4' />
                                                ) : (
                                                    <TbEye className='h-4 w-4' />
                                                )}
                                            </Button>
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {passwordSuccess && (
                                <Alert className='border-green-200 bg-green-50'>
                                    <AlertTitle>Success</AlertTitle>
                                    <AlertDescription>{passwordSuccess}</AlertDescription>
                                </Alert>
                            )}

                            {passwordError && (
                                <Alert className='border-red-200 bg-red-50'>
                                    <AlertTitle>Error</AlertTitle>
                                    <AlertDescription>{passwordError}</AlertDescription>
                                </Alert>
                            )}

                            <Button type='submit' disabled={isPasswordLoading}>
                                {isPasswordLoading ? 'Updating...' : 'Change Password'}
                            </Button>
                        </form>
                    </Form>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                        <TbShield className='h-5 w-5 text-primary' />
                        Two-Factor Authentication
                    </CardTitle>
                    <CardDescription>
                        Add an extra layer of security to your account
                    </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                    <div className='flex items-center justify-between rounded-lg border p-4'>
                        <div>
                            <h3 className='font-medium'>Two-Factor Authentication</h3>
                            <p className='text-sm text-gray-500'>Protect your account with 2FA</p>
                        </div>
                        <Button variant='outline'>Enable</Button>
                    </div>
                </CardContent>
            </Card>
        </>
    );
}
