import { create } from 'zustand';

// Define the zoom store state interface
interface ZoomStoreState {
    // Zoom-related state can be added here if needed
    // For now, most zoom functionality is handled by the useMapZoom hook
    
    // Placeholder for future zoom-related state management
    isZooming: boolean;
    setIsZooming: (isZooming: boolean) => void;
}

export const useZoomStore = create<ZoomStoreState>((set, get) => ({
    isZooming: false,
    
    setIsZooming: (isZooming) => {
        set({ isZooming });
    }
}));
