import React from 'react';
import { TiptapMarkdownEditor } from './TiptapMarkdownEditor';

interface NodeSectionsEditorProps {
    sectionId?: string;
    initialValue?: string;
    onSave?: (value: string) => void;
    onCancel?: () => void;
    clickPosition?: { x: number; y: number } | null;
}

export function NodeSectionsEditor({
    sectionId = '',
    initialValue = '',
    onSave,
    onCancel,
    clickPosition
}: NodeSectionsEditorProps) {
    return (
        <div className='section-editor'>
            <TiptapMarkdownEditor
                initialValue={initialValue}
                onSave={onSave}
                onCancel={onCancel}
                placeholder='Enter section content...'
                clickPosition={clickPosition || undefined}
            />
        </div>
    );
}
