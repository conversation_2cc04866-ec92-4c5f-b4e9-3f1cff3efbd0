import React from 'react';
import { TiptapMarkdownEditor } from './TiptapMarkdownEditor';

interface NodeSectionsEditorProps {
    sectionId?: string;
    initialValue?: string;
    onSave?: (value: string) => void;
    onCancel?: () => void;
}

export function NodeSectionsEditor({
    sectionId = '',
    initialValue = '',
    onSave,
    onCancel
}: NodeSectionsEditorProps) {
    return (
        <div className='section-editor'>
            <TiptapMarkdownEditor
                initialValue={initialValue}
                onSave={onSave}
                onCancel={onCancel}
                placeholder='Enter section content...'
            />
        </div>
    );
}
