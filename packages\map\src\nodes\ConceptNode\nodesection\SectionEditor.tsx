import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@repo/ui';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';

interface NodeSectionsEditorProps {
    sectionId?: string;
    initialValue?: string;
    onSave?: (value: string) => void;
    onCancel?: () => void;
}

export function NodeSectionsEditor({
    sectionId = '',
    initialValue = '',
    onSave,
    onCancel
}: NodeSectionsEditorProps) {
    const [value, setValue] = useState(initialValue);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Auto-resize textarea and focus on mount
    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.focus();
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, []);

    // Handle textarea value change
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setValue(e.target.value);

        // Auto-resize textarea
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    };

    // Handle save
    const handleSave = () => {
        if (onSave) {
            onSave(value);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        if (onCancel) {
            onCancel();
        }
    };

    // Handle keyboard shortcuts
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        // Save on Ctrl+Enter or Cmd+Enter
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            handleSave();
        }

        // Cancel on Escape
        if (e.key === 'Escape') {
            e.preventDefault();
            handleCancel();
        }
    };

    return (
        <div className='section-editor'>
            <textarea
                ref={textareaRef}
                value={value}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                className='min-h-[100px] w-full rounded-md border border-gray-200 bg-white p-2 text-sm text-gray-800 focus:outline-none focus:ring-1 focus:ring-blue-400'
                placeholder='Enter section content...'
            />
            <div className='mt-2 flex justify-end gap-2'>
                <Button variant='outline' size='sm' onClick={handleCancel} className='text-xs'>
                    Cancel
                </Button>
                <Button variant='default' size='sm' onClick={handleSave} className='text-xs'>
                    Save
                </Button>
            </div>
        </div>
    );
}
