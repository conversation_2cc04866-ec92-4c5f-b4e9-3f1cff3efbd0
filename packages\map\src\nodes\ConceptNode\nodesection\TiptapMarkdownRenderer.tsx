import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';

interface TiptapMarkdownRendererProps {
    content?: string;
    onClick?: () => void;
    className?: string;
    placeholder?: string;
}

export function TiptapMarkdownRenderer({
    content = '',
    onClick,
    className = '',
    placeholder = 'Click to edit this section...'
}: TiptapMarkdownRendererProps) {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                // Configure extensions for read-only display
                codeBlock: {
                    HTMLAttributes: {
                        class: 'code-block-wrapper'
                    }
                }
            }),
            Markdown.configure({
                html: false,
                transformCopiedText: false,
                transformPastedText: false
            })
        ],
        content: content,
        editable: false,
        editorProps: {
            attributes: {
                class: 'markdown-content prose prose-sm max-w-none focus:outline-none'
            }
        },
        immediatelyRender: false
    });

    // Update content when prop changes
    React.useEffect(() => {
        if (editor && content !== editor.storage.markdown.getMarkdown()) {
            editor.commands.setContent(content);
        }
    }, [editor, content]);

    if (!editor) {
        return null;
    }

    const isEmpty = !content || content.trim() === '';

    return (
        <div 
            onClick={onClick}
            className={`min-h-[20px] cursor-text rounded p-0 hover:bg-gray-100/50 ${className}`}
            style={{ lineHeight: '1.5' }}
        >
            {isEmpty ? (
                <span className="text-xs italic text-gray-400">
                    {placeholder}
                </span>
            ) : (
                <EditorContent 
                    editor={editor} 
                    className="w-full max-w-full overflow-hidden break-words"
                />
            )}
        </div>
    );
}
