import { Edge } from '@xyflow/react';

/**
 * Handles hover effects on edge badges
 * @param edgeId The ID of the edge to apply hover effect to
 * @param isHovering Whether the badge is being hovered
 * @param setEdges Function to update edges
 */
export const handleEdgeBadgeHover = (
  edgeId: string,
  isHovering: boolean,
  setEdges: (updater: (edges: Edge[]) => Edge[]) => void
) => {
  setEdges(eds =>
    eds.map(edge => {
      if (edge.id === edgeId) {
        return { ...edge, animated: isHovering };
      }
      return edge;
    })
  );
};
