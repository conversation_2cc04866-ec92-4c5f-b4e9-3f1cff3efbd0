import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { supabase } from '@repo/auth';
import {
    Button,
    Input,
    Card,
    CardHeader,
    CardContent,
    CardTitle,
    CardDescription,
    Form,
    FormField,
    FormItem,
    FormControl,
    FormMessage,
    FormLabel,
    Alert,
    AlertTitle,
    AlertDescription,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@repo/ui';
import { TbUserCircle } from 'react-icons/tb';

// Schema for profile update form
const profileSchema = z.object({
    fullName: z.string().min(2, { message: 'Name must be at least 2 characters' }),
    email: z.string().email({ message: 'Invalid email address' }).optional(),
    username: z.string().min(3, { message: 'Username must be at least 3 characters' }).optional(),
    language: z.string().optional(),
    timezone: z.string().optional(),
    bio: z.string().max(160, { message: 'Bio must be less than 160 characters' }).optional()
});

type ProfileFormValues = z.infer<typeof profileSchema>;

interface ProfileFormProps {
    user: any;
}

export default function ProfileForm({ user }: ProfileFormProps) {
    const [profileSuccess, setProfileSuccess] = useState<string | null>(null);
    const [profileError, setProfileError] = useState<string | null>(null);
    const [isProfileLoading, setIsProfileLoading] = useState(false);

    // Profile form
    const profileForm = useForm<ProfileFormValues>({
        resolver: zodResolver(profileSchema),
        defaultValues: {
            fullName: user?.user_metadata?.full_name || '',
            email: user?.email || '',
            username: user?.user_metadata?.username || '',
            language: user?.user_metadata?.language || 'en',
            timezone: user?.user_metadata?.timezone || 'UTC',
            bio: user?.user_metadata?.bio || ''
        }
    });

    // Handle profile update
    async function onProfileSubmit(values: ProfileFormValues) {
        setIsProfileLoading(true);
        setProfileSuccess(null);
        setProfileError(null);

        try {
            const { error } = await supabase.auth.updateUser({
                data: {
                    full_name: values.fullName,
                    username: values.username,
                    language: values.language,
                    timezone: values.timezone,
                    bio: values.bio
                }
            });

            if (error) {
                throw error;
            }

            setProfileSuccess('Profile updated successfully');
        } catch (error: any) {
            setProfileError(error.message || 'Failed to update profile');
        } finally {
            setIsProfileLoading(false);
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                    <TbUserCircle className='h-5 w-5 text-primary' />
                    Profile Information
                </CardTitle>
                <CardDescription>Update your account profile details</CardDescription>
            </CardHeader>
            <CardContent>
                <Form {...profileForm}>
                    <form
                        onSubmit={profileForm.handleSubmit(onProfileSubmit)}
                        className='space-y-4'
                    >
                        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                            <FormField
                                control={profileForm.control}
                                name='fullName'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Full Name</FormLabel>
                                        <FormControl>
                                            <Input placeholder='Your name' {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={profileForm.control}
                                name='email'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Email</FormLabel>
                                        <FormControl>
                                            <Input placeholder='Your email' {...field} disabled />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={profileForm.control}
                                name='username'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Username</FormLabel>
                                        <FormControl>
                                            <Input placeholder='Your username' {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={profileForm.control}
                                name='language'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Language</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder='Select language' />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value='en'>English</SelectItem>
                                                <SelectItem value='es'>Spanish</SelectItem>
                                                <SelectItem value='fr'>French</SelectItem>
                                                <SelectItem value='de'>German</SelectItem>
                                                <SelectItem value='zh'>Chinese</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={profileForm.control}
                                name='timezone'
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Timezone</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder='Select timezone' />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value='UTC'>UTC</SelectItem>
                                                <SelectItem value='America/New_York'>
                                                    Eastern Time (ET)
                                                </SelectItem>
                                                <SelectItem value='America/Chicago'>
                                                    Central Time (CT)
                                                </SelectItem>
                                                <SelectItem value='America/Denver'>
                                                    Mountain Time (MT)
                                                </SelectItem>
                                                <SelectItem value='America/Los_Angeles'>
                                                    Pacific Time (PT)
                                                </SelectItem>
                                                <SelectItem value='Europe/London'>
                                                    London
                                                </SelectItem>
                                                <SelectItem value='Europe/Paris'>Paris</SelectItem>
                                                <SelectItem value='Asia/Tokyo'>Tokyo</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={profileForm.control}
                            name='bio'
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Bio</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder='A short bio about yourself'
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {profileSuccess && (
                            <Alert className='border-green-200 bg-green-50'>
                                <AlertTitle>Success</AlertTitle>
                                <AlertDescription>{profileSuccess}</AlertDescription>
                            </Alert>
                        )}

                        {profileError && (
                            <Alert className='border-red-200 bg-red-50'>
                                <AlertTitle>Error</AlertTitle>
                                <AlertDescription>{profileError}</AlertDescription>
                            </Alert>
                        )}

                        <Button type='submit' disabled={isProfileLoading}>
                            {isProfileLoading ? 'Updating...' : 'Update Profile'}
                        </Button>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
}
