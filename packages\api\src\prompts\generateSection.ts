import { NODE_SECTIONS } from '@repo/utils';
import { NodeSection } from '../openai';
import {
    formatSectionLabels,
    getLanguageInstruction,
    getExpertIntro,
    getLearningOutcome
} from './promptWrapper';

export const generateSection = (
    sourceConceptTitle: string,
    sectionType: string,
    sourceSections: Record<string, NodeSection>,
    deckTitle?: string,
    learningOutcome?: string,
    language: string = 'en',
    temperature?: number,
    maxTokens?: number
): string => {
    // Find the section definition that matches the requested section type
    const sectionKey = Object.keys(NODE_SECTIONS).find(
        key => NODE_SECTIONS[key as keyof typeof NODE_SECTIONS].defaultTitle === sectionType
    ) as keyof typeof NODE_SECTIONS | undefined;

    // Get the generation prompt for this section type
    const generationPrompt = sectionKey
        ? NODE_SECTIONS[sectionKey].generationPrompt
        : `Create content for the "${sectionType}" section of this concept.`;

    // Format existing sections
    const existingSectionLabels = formatSectionLabels(sourceSections);

    // Build the prompt
    return `${getExpertIntro()} Your task is to generate content for the "${sectionType}" section of the concept
"${sourceConceptTitle}"${deckTitle ? ` in the context of ${deckTitle}` : ''}.

SECTION GENERATION INSTRUCTIONS:
${generationPrompt}

CONTEXT:
This concept already has the following sections: ${existingSectionLabels || 'None yet'}

${getLearningOutcome(learningOutcome)}

The heading will be used as the section title in the UI (dont use markdown formatting for the heading!), and the content will be displayed below it.
${getLanguageInstruction(language)}`;
};
