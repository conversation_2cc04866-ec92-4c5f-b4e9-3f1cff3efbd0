/**
 * Group types define the different types of node groups in the knowledge map.
 * Each group type has specific containment rules and visual styling.
 */
export const GROUP_TYPES = {
  TOPIC_GROUP: {
    type: 'topicGroup',
    label: 'Topic Group',
    description:
      'A group that can contain only nodes. Can be connected to other nodes and topic groups.',
    color: 'bg-blue-500/10 border-blue-300/50',
    canContain: ['node']
  },
  SUBJECT_GROUP: {
    type: 'subjectGroup',
    label: 'Subject Group',
    description: 'A higher-level group that can contain both nodes and topic groups.',
    color: 'bg-gray-500/10 border-gray-300/50',
    canContain: ['node', 'topicGroup']
  }
};
