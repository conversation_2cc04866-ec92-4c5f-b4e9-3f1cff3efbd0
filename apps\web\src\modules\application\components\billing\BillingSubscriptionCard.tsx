import { useState } from 'react';
import { supabase } from '@repo/auth';
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>nt,
    Card<PERSON>ooter,
    CardTitle,
    CardDescription,
    Badge
} from '@repo/ui';
import { TbCrown } from 'react-icons/tb';

interface Subscription {
    id: string;
    status: 'active' | 'canceled' | 'incomplete' | 'past_due' | 'trialing';
    current_period_end: string;
    cancel_at_period_end: boolean;
    plan: 'monthly' | 'annual';
    created_at: string;
    price_id: string;
    start_date: string;
    trial_end?: string;
    next_billing_date: string;
    payment_method?: any;
}

interface BillingSubscriptionCardProps {
    subscription: Subscription | null;
    isProcessing: boolean;
    onCancelSubscription: () => Promise<void>;
    formatDate: (dateString: string) => string;
}

export default function BillingSubscriptionCard({
    subscription,
    isProcessing,
    onCancelSubscription,
    formatDate
}: BillingSubscriptionCardProps) {
    if (!subscription || subscription.status !== 'active') {
        return null;
    }

    return (
        <Card className='mb-8'>
            <CardHeader>
                <div className='flex items-center justify-between'>
                    <div>
                        <CardTitle>Current Subscription</CardTitle>
                        <CardDescription>Manage your subscription</CardDescription>
                    </div>
                    <Badge className='bg-amber-500 hover:bg-amber-600'>
                        <TbCrown className='mr-1 h-3.5 w-3.5' />
                        Pro
                    </Badge>
                </div>
            </CardHeader>
            <CardContent>
                <div className='space-y-4'>
                    <div>
                        <p className='text-sm font-medium'>Status</p>
                        <p className='text-sm text-gray-500'>
                            {subscription.cancel_at_period_end
                                ? 'Active (Cancels at end of billing period)'
                                : 'Active'}
                        </p>
                    </div>
                    <div>
                        <p className='text-sm font-medium'>Current Period Ends</p>
                        <p className='text-sm text-gray-500'>
                            {formatDate(subscription.current_period_end)}
                        </p>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                {!subscription.cancel_at_period_end && (
                    <Button
                        variant='outline'
                        onClick={onCancelSubscription}
                        disabled={isProcessing}
                    >
                        {isProcessing ? 'Processing...' : 'Cancel Subscription'}
                    </Button>
                )}
            </CardFooter>
        </Card>
    );
}
