import { generateSectionContent, NodeSection, NodeTag } from '@repo/api';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';

export const handleGenerateSectionContent = async (
    section: SectionConfig,
    conceptTitle: string,
    sections: Record<string, SectionConfig>,
    tags: { name: string; type: string }[],
    setLoadingSections: (
        updater: (prev: Record<string, boolean>) => Record<string, boolean>
    ) => void,
    setGenerationError: (updater: (prev: Record<string, string>) => Record<string, string>) => void,
    updateNodeData: (data: Record<string, any>) => void
): Promise<void> => {
    const sectionId = section.id;
    const sectionLabel = section.label;

    try {
        console.log(`Generating content for section:`, section);

        // Set loading state for this section
        setLoadingSections(prev => ({ ...prev, [sectionId]: true }));

        // Clear any previous errors
        setGenerationError(prev => {
            const newErrors = { ...prev };
            delete newErrors[sectionId];
            return newErrors;
        });

        // Convert sections to the format expected by the API
        const apiSections: Record<string, NodeSection> = {};
        Object.entries(sections).forEach(([key, section]) => {
            apiSections[key] = {
                id: section.id,
                label: section.label,
                icon: section.icon as string,
                value: section.value,
                collapsible: section.collapsible
            };
        });

        // Convert tags to the format expected by the API
        const apiTags: NodeTag[] = tags.map(tag => ({
            name: tag.name,
            type: tag.type
        }));

        // Call the API to generate content
        const generatedContent = await generateSectionContent(
            conceptTitle,
            sectionLabel,
            apiTags,
            apiSections
        );

        // Create a complete section object with all properties preserved
        const updatedSection: SectionConfig = {
            id: section.id,
            label: section.label,
            icon: section.icon,
            value: generatedContent,
            collapsible: section.collapsible ?? true
        };

        // Update the sections object
        const updatedSections = {
            ...sections,
            [sectionId]: updatedSection
        };

        // Update the node data with the new sections
        updateNodeData({ sections: updatedSections });
    } catch (error) {
        console.error('Error generating section content:', error);
        setGenerationError(prev => ({
            ...prev,
            [sectionId]: 'Failed to generate content. Click to try again.'
        }));
    } finally {
        // Clear loading state
        setLoadingSections(prev => {
            const newLoading = { ...prev };
            delete newLoading[sectionId];
            return newLoading;
        });
    }
};
