import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardTitle, CardDescription, Badge } from '@repo/ui';
import { TbDevices } from 'react-icons/tb';

interface Device {
    id: number;
    name: string;
    lastActive: string;
    current: boolean;
}

export default function DevicesList() {
    const [activeDevices, setActiveDevices] = useState<Device[]>([
        { id: 1, name: 'Chrome on Windows', lastActive: '2 minutes ago', current: true },
        { id: 2, name: 'Mobile App on iPhone', lastActive: '2 days ago', current: false }
    ]);

    // Handle device removal
    const handleRemoveDevice = (deviceId: number) => {
        setActiveDevices(prev => prev.filter(device => device.id !== deviceId));
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                    <TbDevices className='h-5 w-5 text-primary' />
                    Active Devices
                </CardTitle>
                <CardDescription>Manage devices that are logged into your account</CardDescription>
            </CardHeader>
            <CardContent>
                <div className='space-y-4'>
                    {activeDevices.map(device => (
                        <div
                            key={device.id}
                            className='flex items-center justify-between rounded-lg border p-4'
                        >
                            <div>
                                <h3 className='font-medium'>{device.name}</h3>
                                <p className='text-sm text-gray-500'>
                                    Last active: {device.lastActive}
                                </p>
                                {device.current && (
                                    <Badge className='mt-1 bg-green-100 text-green-800'>
                                        Current Device
                                    </Badge>
                                )}
                            </div>
                            {!device.current && (
                                <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() => handleRemoveDevice(device.id)}
                                >
                                    Sign Out
                                </Button>
                            )}
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
}
