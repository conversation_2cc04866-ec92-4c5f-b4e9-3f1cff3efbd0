import { Node } from '@xyflow/react';

/**
 * Handles the selection of a node for creating a link/edge
 * @param nodeId The ID of the node to link to
 * @param getNode Function to get a node by ID
 * @param setSelectedNodeToLink Function to set the selected node
 * @param setLinkPopoverOpen Function to control link popover visibility
 * @param setEdgeTypePopoverOpen Function to control edge type popover visibility
 */
export const handleNodeSelectForTagging = (
  nodeId: string,
  getNode: (id: string) => Node | undefined,
  setSelectedNodeToLink: (node: Node | null) => void,
  setLinkPopoverOpen: (open: boolean) => void,
  setEdgeTypePopoverOpen: (open: boolean) => void
) => {
  const node = getNode(nodeId);
  if (node) {
    console.log(
      `[packages/ui/map/features/conceptnode/utils/handleNodeSelectForTagging.ts] Selected node ${nodeId} for linking`
    );
    setSelectedNodeToLink(node);
    setLinkPopoverOpen(false); // Close search popover
    setEdgeTypePopoverOpen(true); // Open edge type selection popover
  }
};
