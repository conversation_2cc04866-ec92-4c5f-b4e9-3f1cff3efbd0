import { NODE_SECTIONS } from '@repo/utils';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';

export const handleAddSection = async (
    startEditingSectionLabel: (sectionId: string, label: string) => void,
    generateSectionContentWithLLM: (section: SectionConfig) => Promise<void>,
    sectionConfig?: Partial<SectionConfig>
): Promise<SectionConfig> => {
    // Use provided config or defaults
    const sectionId = crypto.randomUUID();
    const newSection: SectionConfig = {
        id: sectionId,
        label: sectionConfig?.label || 'New Section',
        icon: sectionConfig?.icon || '📝',
        value: sectionConfig?.value || '', // Default value is empty string
        collapsible: sectionConfig?.collapsible ?? true
    };

    // For custom sections, immediately start editing the label
    if (sectionConfig?.label === 'Custom Section') {
        // Wait for the next render cycle to ensure the section is in the DOM
        setTimeout(() => {
            startEditingSectionLabel(sectionId, newSection.label);
        }, 50);
        return newSection;
    }

    return newSection;
};
