import React from 'react';
import {
    <PERSON>Edge,
    EdgeLabelRenderer,
    getBezierPath,
    EdgeProps,
    useReactFlow,
    useInternalNode,
    Position
} from '@xyflow/react';
import { EDGE_TYPES } from '@repo/utils';
import { getSimpleFloatingEdgeParams } from '../../../edges/edgeUtils';
import { TbChevronsLeft, TbChevronsRight, TbChevronsUp, TbChevronsDown } from 'react-icons/tb';

const defaultStyle: React.CSSProperties & { strokeDasharray?: string } = {
    stroke: '#d9d9d9', // Even lighter grey as requested
    strokeWidth: 1.5
};

// We're removing the default markers as requested
// No markers will be applied to the edges

const typeStylesCache: Record<string, { style: React.CSSProperties }> = {};

function getEdgeStyle(type: string, isSelected: boolean) {
    // Return default style for non-selected edges
    if (!isSelected) {
        return { style: defaultStyle };
    }

    if (typeStylesCache[type]) {
        return typeStylesCache[type];
    }

    // Initialize variables to hold style
    let specificStyle: React.CSSProperties & { strokeDasharray?: string } = { ...defaultStyle };

    // Extract color from EDGE_TYPES if available
    if (isValidEdgeType(type) && EDGE_TYPES[type]?.color) {
        const colorClass = EDGE_TYPES[type].color;
        // Extract the color value from the Tailwind class
        // Format is typically 'text-color-400' or 'bg-color-500/10'
        const colorMatch = colorClass.match(/text-([a-z]+-\d+)/);
        if (colorMatch && colorMatch[1]) {
            const colorBase = colorMatch[1]; // e.g., 'purple-400'
            const colorParts = colorBase.split('-');
            const colorName = colorParts[0]; // e.g., 'purple'

            // Map Tailwind color names to hex values
            const colorMap: Record<string, string> = {
                purple: '#a78bfa',
                indigo: '#818cf8',
                amber: '#fbbf24',
                cyan: '#22d3ee',
                green: '#34d399',
                red: '#f87171',
                rose: '#fb7185',
                blue: '#60a5fa',
                gray: '#9ca3af'
            };

            const color = colorMap[colorName] || '#a78bfa'; // Default to purple if not found

            specificStyle = { stroke: color, strokeWidth: 2 };

            // Add dash patterns for specific edge types if needed
            if (type === 'componentOf') {
                // No dash pattern for componentOf
            } else if (type === 'prerequisiteFor') {
                specificStyle.strokeDasharray = '6 3';
            } else if (type === 'causes') {
                // No dash pattern for causes
            } else if (type === 'contrasts') {
                specificStyle.strokeDasharray = '2 2';
            }
        }
    } else {
        // Fallback for unknown types
        specificStyle = { ...defaultStyle };
    }

    typeStylesCache[type] = { style: specificStyle };
    return typeStylesCache[type];
}

function isValidEdgeType(type: string): type is keyof typeof EDGE_TYPES {
    return type in EDGE_TYPES;
}

// Helper function to determine if an edge should be active based on node selection
function shouldEdgeBeActive(sourceNode: any, targetNode: any, selected: boolean) {
    // Edge is active if it's selected or if either of its connected nodes is selected
    return selected || (sourceNode && sourceNode.selected) || (targetNode && targetNode.selected);
}

// Helper function to determine which chevron icon to use based on the position
function getChevronIcon(position: Position) {
    switch (position) {
        case Position.Left:
            return TbChevronsLeft;
        case Position.Right:
            return TbChevronsRight;
        case Position.Top:
            return TbChevronsUp;
        case Position.Bottom:
            return TbChevronsDown;
        default:
            return TbChevronsRight; // Default to right
    }
}

export function CustomEdge(props: EdgeProps) {
    const { id, source, target, data, selected } = props;

    const sourceNode = useInternalNode(source);
    const targetNode = useInternalNode(target);
    const { setEdges } = useReactFlow();

    if (!sourceNode || !targetNode) {
        return null;
    }

    const type = typeof data?.edgeType === 'string' ? data.edgeType : 'componentOf';
    const currentType = isValidEdgeType(type) ? type : 'componentOf';

    // Get titles from EDGE_TYPES if available
    const sourceToTargetTitle = isValidEdgeType(currentType)
        ? EDGE_TYPES[currentType].title
        : currentType;
    const targetToSourceTitle = isValidEdgeType(currentType)
        ? EDGE_TYPES[currentType].reversedTitle
        : `reversed ${currentType}`;

    // Check if edge should be active (either selected or connected to a selected node)
    const isActive = shouldEdgeBeActive(sourceNode, targetNode, !!selected);

    // Get style based on active state
    const { style } = getEdgeStyle(currentType, isActive);

    const { sx, sy, tx, ty, sourcePos, targetPos } = getSimpleFloatingEdgeParams(
        sourceNode,
        targetNode
    );

    const [edgePath] = getBezierPath({
        sourceX: sx,
        sourceY: sy,
        targetX: tx,
        targetY: ty,
        sourcePosition: sourcePos,
        targetPosition: targetPos
    });

    // Calculate positions for both labels - position them on the actual Bezier curve
    // We need to calculate points on the actual Bezier curve, not just linear interpolation

    // Helper function to calculate a point on a cubic Bezier curve
    const calculateBezierPoint = (
        t: number,
        startX: number,
        startY: number,
        endX: number,
        endY: number,
        sourcePos: Position,
        targetPos: Position
    ) => {
        // Get control points based on source and target positions
        // These calculations match what getBezierPath does internally
        const deltaX = endX - startX;
        const deltaY = endY - startY;

        // Control point offsets - these values match the internal calculations in getBezierPath
        let offsetX = Math.abs(deltaX) * 0.3;
        let offsetY = Math.abs(deltaY) * 0.3;

        // Ensure minimum offset for short edges
        offsetX = Math.max(offsetX, 25);
        offsetY = Math.max(offsetY, 25);

        // Set control points based on handle positions
        let [sourceControlX, sourceControlY] = [startX, startY];
        let [targetControlX, targetControlY] = [endX, endY];

        // Adjust control points based on source/target positions
        if (sourcePos === Position.Left) sourceControlX = startX - offsetX;
        if (sourcePos === Position.Right) sourceControlX = startX + offsetX;
        if (sourcePos === Position.Top) sourceControlY = startY - offsetY;
        if (sourcePos === Position.Bottom) sourceControlY = startY + offsetY;

        if (targetPos === Position.Left) targetControlX = endX - offsetX;
        if (targetPos === Position.Right) targetControlX = endX + offsetX;
        if (targetPos === Position.Top) targetControlY = endY - offsetY;
        if (targetPos === Position.Bottom) targetControlY = endY + offsetY;

        // Calculate point on cubic Bezier curve using the parameter t (0 to 1)
        const x =
            Math.pow(1 - t, 3) * startX +
            3 * Math.pow(1 - t, 2) * t * sourceControlX +
            3 * (1 - t) * Math.pow(t, 2) * targetControlX +
            Math.pow(t, 3) * endX;

        const y =
            Math.pow(1 - t, 3) * startY +
            3 * Math.pow(1 - t, 2) * t * sourceControlY +
            3 * (1 - t) * Math.pow(t, 2) * targetControlY +
            Math.pow(t, 3) * endY;

        return { x, y };
    };

    // Source to target label (forward direction) - positioned at 1/4 of the path
    const forwardPoint = calculateBezierPoint(0.25, sx, sy, tx, ty, sourcePos, targetPos);
    const forwardLabelX = forwardPoint.x;
    const forwardLabelY = forwardPoint.y;

    // Target to source label (reverse direction) - positioned at 3/4 of the path
    const reversePoint = calculateBezierPoint(0.75, sx, sy, tx, ty, sourcePos, targetPos);
    const reverseLabelX = reversePoint.x;
    const reverseLabelY = reversePoint.y;

    const cycleEdgeType = () => {
        const edgeTypeKeys = Object.keys(EDGE_TYPES) as (keyof typeof EDGE_TYPES)[];
        const currentIndex = edgeTypeKeys.indexOf(currentType);
        const nextIndex = (currentIndex + 1) % edgeTypeKeys.length;
        const nextType = edgeTypeKeys[nextIndex];

        console.log(
            `[packages/ui/src/components/map/CustomEdges.tsx:cycleEdgeType] Cycling edge ${id} type from ${currentType} to ${nextType}`
        );

        setEdges(eds =>
            eds.map(edge => {
                if (edge.id === id) {
                    const { style: nextStyle } = getEdgeStyle(nextType, isActive);

                    return {
                        ...edge,
                        data: { ...edge.data, edgeType: nextType },
                        type: nextType,
                        style: nextStyle
                    };
                }
                return edge;
            })
        );
    };

    return (
        <g>
            {/* Simple edge without markers */}
            <BaseEdge path={edgePath} style={style} />

            {/* Source to Target Label (Forward) */}
            <EdgeLabelRenderer>
                <div
                    style={{
                        position: 'absolute',
                        transform: `translate(-50%, -50%) translate(${forwardLabelX}px,${forwardLabelY}px)`,
                        pointerEvents: 'all',
                        // Calculate the angle of the edge at this point to rotate the label
                        // This ensures the label is aligned with the edge direction
                        zIndex: 1000 // Ensure label is above the edge
                    }}
                    className='nodrag nopan'
                >
                    <button
                        onClick={cycleEdgeType}
                        className={`flex cursor-pointer items-center gap-1 rounded border px-1.5 py-0.5 text-xs shadow-sm transition-all hover:border-gray-300 hover:shadow-md ${isActive ? 'border-gray-200 bg-white' : 'border-gray-100 bg-gray-50 text-gray-400'}`}
                        title={
                            EDGE_TYPES[currentType]?.description ||
                            `Click to change type (current: ${currentType})`
                        }
                        disabled={!isActive}
                    >
                        <span className={!isActive ? 'opacity-50' : ''}>
                            {EDGE_TYPES[currentType]?.icon}
                        </span>
                        <span
                            className={
                                !isActive
                                    ? 'text-gray-400'
                                    : isValidEdgeType(currentType)
                                      ? EDGE_TYPES[currentType].color
                                      : ''
                            }
                        >
                            {sourceToTargetTitle}
                        </span>
                        <span
                            className={
                                !isActive
                                    ? 'text-gray-400'
                                    : isValidEdgeType(currentType)
                                      ? EDGE_TYPES[currentType].color
                                      : ''
                            }
                        >
                            {React.createElement(getChevronIcon(sourcePos), { size: 20 })}
                        </span>
                    </button>
                </div>
            </EdgeLabelRenderer>

            {/* Target to Source Label (Reverse) */}
            <EdgeLabelRenderer>
                <div
                    style={{
                        position: 'absolute',
                        transform: `translate(-50%, -50%) translate(${reverseLabelX}px,${reverseLabelY}px)`,
                        pointerEvents: 'all',
                        // Calculate the angle of the edge at this point to rotate the label
                        // This ensures the label is aligned with the edge direction
                        zIndex: 1000 // Ensure label is above the edge
                    }}
                    className='nodrag nopan'
                >
                    <button
                        onClick={cycleEdgeType}
                        className={`flex cursor-pointer items-center gap-1 rounded border px-1.5 py-0.5 text-xs shadow-sm transition-all hover:border-gray-300 hover:shadow-md ${isActive ? 'border-gray-200 bg-white' : 'border-gray-100 bg-gray-50 text-gray-400'}`}
                        title={`Reversed: ${EDGE_TYPES[currentType]?.description || currentType}`}
                        disabled={!isActive}
                    >
                        <span
                            className={
                                !isActive
                                    ? 'text-gray-400'
                                    : isValidEdgeType(currentType)
                                      ? EDGE_TYPES[currentType].color
                                      : ''
                            }
                        >
                            {React.createElement(getChevronIcon(targetPos), { size: 20 })}
                        </span>
                        <span
                            className={
                                !isActive
                                    ? 'text-gray-400'
                                    : isValidEdgeType(currentType)
                                      ? EDGE_TYPES[currentType].color
                                      : ''
                            }
                        >
                            {targetToSourceTitle}
                        </span>
                        <span className={!isActive ? 'opacity-50' : ''}>
                            {EDGE_TYPES[currentType]?.icon}
                        </span>
                    </button>
                </div>
            </EdgeLabelRenderer>
        </g>
    );
}

// Dynamically create edge types from MapConstants.ts
export const edgeTypes = Object.keys(EDGE_TYPES).reduce(
    (acc, type) => ({
        ...acc,
        [type]: CustomEdge
    }),
    { custom: CustomEdge } // Add a custom type as fallback
);
