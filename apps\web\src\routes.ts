import { route, type RouteConfig } from '@react-router/dev/routes';

export default [
    route('/', 'modules/marketing/routes/landing.tsx'),
    route('/login', 'modules/marketing/routes/login.tsx'),
    route('/register', 'modules/marketing/routes/register.tsx'),
    route('/app', 'modules/application/routes/app_root.tsx', [
        route('dashboard', 'modules/application/routes/dashboard.tsx'),
        route('maps', 'modules/application/routes/maps.tsx', [
            route(':map_id', 'modules/application/routes/map_id.tsx')
        ]),
        route('community', 'modules/application/routes/community.tsx'),
        route('analytics', 'modules/application/routes/analytics.tsx'),
        route('account', 'modules/application/routes/account.tsx'),
        route('billing', 'modules/application/routes/billing.tsx')
    ])
] satisfies RouteConfig;
