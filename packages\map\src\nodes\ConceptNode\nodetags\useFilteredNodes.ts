import { useCallback } from 'react';
import { Node, useReactFlow } from '@xyflow/react';
import { MapNodeData } from '../../../Map/interfaces/MapNodeData';

/**
 * Custom hook to filter nodes based on search query
 * @param currentNodeId The ID of the current node to exclude from results
 * @param searchQuery The search query to filter nodes by
 * @returns Array of filtered nodes
 */
export const useFilteredNodes = (currentNodeId: string, searchQuery: string) => {
    const { getNodes } = useReactFlow();

    const getFilteredNodes = useCallback(() => {
        const allNodes = getNodes();
        return allNodes.filter(
            (node: Node<MapNodeData>) =>
                node.id !== currentNodeId && // Exclude self
                // Access conceptTitle safely
                node.data?.conceptTitle?.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }, [getNodes, currentNodeId, searchQuery]);

    return getFilteredNodes();
};
