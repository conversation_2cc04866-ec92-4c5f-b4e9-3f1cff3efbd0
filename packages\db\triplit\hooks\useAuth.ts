import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { supabase } from '../auth/supabase';

type AuthAttributesType = {
  user: any;
  isLoadingAuth: boolean;
  error: string | null;
};

type AuthMethodsType = {
  setUser: (user: any) => void;
  setIsLoadingAuth: (isLoadingAuth: boolean) => void;
  setError: (error: string | null) => void;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<{ success: boolean; error?: string }>;
  signUp: (
    email: string,
    password: string,
    name?: string
  ) => Promise<{ success: boolean; error?: string }>;
};

type AuthType = AuthAttributesType & AuthMethodsType;

export const useAuth = create<AuthType>()(
  persist(
    set => ({
      user: null,
      isLoadingAuth: false,
      error: null,
      setUser: user => set({ user }),
      setIsLoadingAuth: isLoadingAuth => set({ isLoadingAuth }),
      setError: error => set({ error }),

      signIn: async (email: string, password: string) => {
        try {
          set({ isLoadingAuth: true, error: null });

          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
          });

          if (error) {
            set({ error: error.message, isLoadingAuth: false });
            return { success: false, error: error.message };
          }

          if (!data.user) {
            set({ error: 'Login failed', isLoadingAuth: false });
            return { success: false, error: 'Login failed' };
          }

          set({ user: data.user, isLoadingAuth: false });
          console.log('useAuth:signIn:: ' + JSON.stringify(data.user));
          return { success: true };
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Login failed';
          set({ error: errorMessage, isLoadingAuth: false });
          return { success: false, error: errorMessage };
        }
      },

      signOut: async () => {
        try {
          set({ isLoadingAuth: true, error: null });

          const { error } = await supabase.auth.signOut();

          if (error) {
            set({ error: error.message, isLoadingAuth: false });
            return { success: false, error: error.message };
          }

          set({ user: null, isLoadingAuth: false });
          return { success: true };
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Logout failed';
          set({ error: errorMessage, isLoadingAuth: false });
          return { success: false, error: errorMessage };
        }
      },

      signUp: async (email: string, password: string, name?: string) => {
        try {
          set({ isLoadingAuth: true, error: null });

          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                name: name || email.split('@')[0]
              }
            }
          });

          if (error) {
            set({ error: error.message, isLoadingAuth: false });
            return { success: false, error: error.message };
          }

          // Note: Supabase signUp doesn't automatically log the user in if email confirmation is required
          // The user object will still be returned, but they'll need to confirm their email
          set({ user: data.user, isLoadingAuth: false });
          return { success: true };
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Registration failed';
          set({ error: errorMessage, isLoadingAuth: false });
          return { success: false, error: errorMessage };
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({ user: state.user })
    }
  )
);
