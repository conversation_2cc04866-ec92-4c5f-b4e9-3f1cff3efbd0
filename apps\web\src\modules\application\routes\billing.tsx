import { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON><PERSON>oot<PERSON>,
    CardTitle,
    CardDescription,
    Alert,
    AlertTitle,
    AlertDescription,
    Badge
} from '@repo/ui';
import {
    TbCheck,
    TbCrown,
    TbBrain,
    TbCloudUpload,
    TbChartBar,
    TbCalendarTime,
    TbFileInvoice,
    TbDeviceLaptop,
    TbHeadset,
    TbDatabase,
    TbRocket,
    TbPuzzle,
    TbBrandOpenai
} from 'react-icons/tb';

// Mock subscription data - in a real app, this would come from your Stripe webhook handler
interface Subscription {
    id: string;
    status: 'active' | 'canceled' | 'incomplete' | 'past_due' | 'trialing';
    current_period_end: string;
    cancel_at_period_end: boolean;
    plan: 'monthly' | 'annual';
    created_at: string;
    price_id: string;
    start_date: string;
    trial_end?: string;
    next_billing_date: string;
    payment_method?: PaymentMethod;
}

// Mock payment method data
interface PaymentMethod {
    id: string;
    brand: 'visa' | 'mastercard' | 'amex' | 'discover';
    last4: string;
    exp_month: number;
    exp_year: number;
    billing_details?: {
        name: string;
        email: string;
        address?: {
            line1: string;
            line2?: string;
            city: string;
            state: string;
            postal_code: string;
            country: string;
        };
    };
}

// Mock invoice data
interface Invoice {
    id: string;
    amount: number;
    status: 'paid' | 'open' | 'void';
    created: string;
    pdf_url: string;
    description: string;
    period_start: string;
    period_end: string;
    payment_method?: string;
    items?: {
        description: string;
        amount: number;
        quantity: number;
    }[];
}

// Mock usage data
interface UsageData {
    decks: { total: number; limit: number; percentage: number };
    storage: { used: number; limit: number; percentage: number };
    concepts: { total: number; limit: number; percentage: number };
    documents: { total: number; limit: number; percentage: number };
    ai_credits: { used: number; limit: number; percentage: number; reset_date: string };
}

// Feature comparison data
interface FeatureComparison {
    category: string;
    features: {
        name: string;
        description: string;
        free: boolean | string;
        pro: boolean | string;
        icon: React.ReactNode;
    }[];
}

// Pricing tier data
interface PricingTier {
    id: string;
    name: string;
    description: string;
    monthly_price: number;
    annual_price: number;
    features: string[];
    highlighted?: boolean;
    cta: string;
    disabled?: boolean;
}

// FAQ item
interface FAQItem {
    question: string;
    answer: string;
}

// Define pricing tiers
const pricingTiers: PricingTier[] = [
    {
        id: 'free',
        name: 'Free',
        description: 'Basic features for personal use',
        monthly_price: 0,
        annual_price: 0,
        features: [
            'Up to 5 decks',
            'Basic flashcard types',
            'Standard spaced repetition',
            '100MB storage',
            'Community support'
        ],
        cta: 'Current Plan',
        disabled: true
    },
    {
        id: 'pro',
        name: 'Pro',
        description: 'Advanced features for power users',
        monthly_price: 19.99,
        annual_price: 149.99,
        features: [
            'Unlimited decks',
            'All flashcard types',
            'Advanced FSRS algorithm',
            '10GB storage',
            'Unlimited documents',
            'Advanced analytics',
            'AI-powered learning tools',
            'Priority support',
            'Early access to new features'
        ],
        highlighted: true,
        cta: 'Upgrade to Pro'
    }
];

// Define feature comparison data
const featureComparisons: FeatureComparison[] = [
    {
        category: 'Core Features',
        features: [
            {
                name: 'Decks',
                description: 'Create and manage flashcard decks',
                free: 'Up to 5',
                pro: 'Unlimited',
                icon: <TbDatabase className='h-5 w-5 text-blue-500' />
            },
            {
                name: 'Flashcard Types',
                description: 'Different types of flashcards for varied learning',
                free: 'Basic only',
                pro: 'All types',
                icon: <TbPuzzle className='h-5 w-5 text-purple-500' />
            },
            {
                name: 'Spaced Repetition',
                description: 'Algorithm for optimal learning intervals',
                free: 'Standard',
                pro: 'Advanced FSRS',
                icon: <TbCalendarTime className='h-5 w-5 text-green-500' />
            },
            {
                name: 'Storage',
                description: 'Space for your learning materials',
                free: '100MB',
                pro: '10GB',
                icon: <TbCloudUpload className='h-5 w-5 text-amber-500' />
            }
        ]
    },
    {
        category: 'Advanced Features',
        features: [
            {
                name: 'Documents',
                description: 'Upload and study from documents',
                free: 'Up to 3',
                pro: 'Unlimited',
                icon: <TbFileInvoice className='h-5 w-5 text-red-500' />
            },
            {
                name: 'Concept Maps',
                description: 'Visual learning with concept maps',
                free: 'Basic maps',
                pro: 'Advanced maps with AI generation',
                icon: <TbBrain className='h-5 w-5 text-indigo-500' />
            },
            {
                name: 'Analytics',
                description: 'Track your learning progress',
                free: 'Basic stats',
                pro: 'Comprehensive insights',
                icon: <TbChartBar className='h-5 w-5 text-blue-500' />
            },
            {
                name: 'AI Features',
                description: 'AI-powered learning tools',
                free: false,
                pro: true,
                icon: <TbBrandOpenai className='h-5 w-5 text-green-500' />
            }
        ]
    },
    {
        category: 'Support & Access',
        features: [
            {
                name: 'Customer Support',
                description: 'Get help when you need it',
                free: 'Community',
                pro: 'Priority',
                icon: <TbHeadset className='h-5 w-5 text-amber-500' />
            },
            {
                name: 'New Features',
                description: 'Access to new features',
                free: 'Standard',
                pro: 'Early access',
                icon: <TbRocket className='h-5 w-5 text-purple-500' />
            },
            {
                name: 'Sync Across Devices',
                description: 'Use on multiple devices',
                free: true,
                pro: true,
                icon: <TbDeviceLaptop className='h-5 w-5 text-blue-500' />
            }
        ]
    }
];

// Define FAQ items
const faqItems: FAQItem[] = [
    {
        question: 'What payment methods do you accept?',
        answer: 'We accept all major credit cards (Visa, Mastercard, American Express, Discover) through our secure payment processor, Stripe.'
    },
    {
        question: 'Can I cancel my subscription at any time?',
        answer: 'Yes, you can cancel your subscription at any time. Your Pro features will remain active until the end of your current billing period.'
    },
    {
        question: 'Is there a free trial for Pro?',
        answer: "We currently don't offer a free trial, but we do have a 30-day money-back guarantee if you're not satisfied with the Pro features."
    },
    {
        question: 'What happens to my data if I downgrade from Pro to Free?',
        answer: "Your data remains intact, but you'll lose access to Pro-only features. If you have more than 5 decks, you'll still be able to access them but not create new ones until you're below the limit."
    },
    {
        question: 'Do you offer educational or team discounts?',
        answer: 'Yes, we offer special pricing for educational institutions and teams. Please contact our support team for more information.'
    }
];

export default function BillingPage() {
    const { user } = useAuth();
    const [subscription, setSubscription] = useState<Subscription | null>(null);
    const [invoices, setInvoices] = useState<Invoice[]>([]);
    const [usageData, setUsageData] = useState<UsageData>({
        decks: { total: 3, limit: 5, percentage: 60 },
        storage: { used: 25, limit: 100, percentage: 25 },
        concepts: { total: 45, limit: 100, percentage: 45 },
        documents: { total: 2, limit: 3, percentage: 67 },
        ai_credits: {
            used: 75,
            limit: 100,
            percentage: 75,
            reset_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        }
    });
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annual'>('monthly');
    const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
    const [activeTab, setActiveTab] = useState<string>('overview');
    const [showBillingDetails, setShowBillingDetails] = useState<boolean>(false);

    // Fetch subscription data
    useEffect(() => {
        async function fetchSubscription() {
            if (!user) return;

            setIsLoading(true);
            try {
                // In a real app, you would fetch this from your database
                // For now, we'll check if the user has a subscription in their metadata
                const { data, error } = await supabase
                    .from('subscriptions')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();

                if (error && error.code !== 'PGRST116') {
                    throw error;
                }

                // If we have subscription data from Supabase, use it
                if (data) {
                    setSubscription(data);
                } else {
                    // Otherwise, check user metadata for mock subscription
                    const userSubscription = user.user_metadata?.subscription;
                    if (userSubscription) {
                        setSubscription(userSubscription);
                    }
                }

                // Mock payment method data
                if (subscription?.status === 'active') {
                    const mockPaymentMethod: PaymentMethod = {
                        id: 'pm_' + Math.random().toString(36).substring(2, 15),
                        brand: 'visa',
                        last4: '4242',
                        exp_month: 12,
                        exp_year: new Date().getFullYear() + 2,
                        billing_details: {
                            name: user.user_metadata?.full_name || user.email,
                            email: user.email,
                            address: {
                                line1: '123 Main St',
                                city: 'San Francisco',
                                state: 'CA',
                                postal_code: '94105',
                                country: 'US'
                            }
                        }
                    };
                    setPaymentMethod(mockPaymentMethod);
                }

                // Create more detailed mock invoice data
                const currentDate = new Date();
                const mockInvoices: Invoice[] = [
                    {
                        id: 'in_' + Math.random().toString(36).substring(2, 15),
                        amount: billingPeriod === 'monthly' ? 19.99 : 149.99,
                        status: 'paid',
                        created: new Date(
                            currentDate.getTime() - 30 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        pdf_url: '#',
                        description: `Pro Plan - ${billingPeriod === 'monthly' ? 'Monthly' : 'Annual'}`,
                        period_start: new Date(
                            currentDate.getTime() - 60 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        period_end: new Date(
                            currentDate.getTime() - 30 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        payment_method: 'Visa •••• 4242',
                        items: [
                            {
                                description: `Pro Plan - ${billingPeriod === 'monthly' ? 'Monthly' : 'Annual'} Subscription`,
                                amount: billingPeriod === 'monthly' ? 19.99 : 149.99,
                                quantity: 1
                            }
                        ]
                    },
                    {
                        id: 'in_' + Math.random().toString(36).substring(2, 15),
                        amount: billingPeriod === 'monthly' ? 19.99 : 149.99,
                        status: 'paid',
                        created: new Date(
                            currentDate.getTime() - 60 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        pdf_url: '#',
                        description: `Pro Plan - ${billingPeriod === 'monthly' ? 'Monthly' : 'Annual'}`,
                        period_start: new Date(
                            currentDate.getTime() - 90 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        period_end: new Date(
                            currentDate.getTime() - 60 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        payment_method: 'Visa •••• 4242',
                        items: [
                            {
                                description: `Pro Plan - ${billingPeriod === 'monthly' ? 'Monthly' : 'Annual'} Subscription`,
                                amount: billingPeriod === 'monthly' ? 19.99 : 149.99,
                                quantity: 1
                            }
                        ]
                    },
                    {
                        id: 'in_' + Math.random().toString(36).substring(2, 15),
                        amount: billingPeriod === 'monthly' ? 19.99 : 149.99,
                        status: 'paid',
                        created: new Date(
                            currentDate.getTime() - 90 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        pdf_url: '#',
                        description: `Pro Plan - ${billingPeriod === 'monthly' ? 'Monthly' : 'Annual'}`,
                        period_start: new Date(
                            currentDate.getTime() - 120 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        period_end: new Date(
                            currentDate.getTime() - 90 * 24 * 60 * 60 * 1000
                        ).toISOString(),
                        payment_method: 'Visa •••• 4242',
                        items: [
                            {
                                description: `Pro Plan - ${billingPeriod === 'monthly' ? 'Monthly' : 'Annual'} Subscription`,
                                amount: billingPeriod === 'monthly' ? 19.99 : 149.99,
                                quantity: 1
                            }
                        ]
                    }
                ];

                setInvoices(mockInvoices);
            } catch (err: any) {
                console.error('Error fetching subscription:', err);
                setError(err.message || 'Failed to load subscription data');
            } finally {
                setIsLoading(false);
            }
        }

        fetchSubscription();
    }, [user, subscription?.status, billingPeriod]);

    // Get the price based on billing period
    const getPrice = () => {
        return billingPeriod === 'monthly' ? 19.99 : 149.99;
    };

    // Get the savings percentage for annual billing
    const getSavingsPercentage = () => {
        const monthlyTotal = 19.99 * 12;
        const annualPrice = 149.99;
        return Math.round(((monthlyTotal - annualPrice) / monthlyTotal) * 100);
    };

    // Get the annual savings amount
    const getAnnualSavings = () => {
        const monthlyTotal = 19.99 * 12;
        const annualPrice = 149.99;
        return (monthlyTotal - annualPrice).toFixed(2);
    };

    // Format currency for display
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };

    // Format date for display
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    // Format date with time for display
    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Calculate days remaining until date
    const getDaysRemaining = (dateString: string) => {
        const targetDate = new Date(dateString);
        const currentDate = new Date();
        const diffTime = targetDate.getTime() - currentDate.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    // Handle subscription creation
    const handleSubscribe = async () => {
        if (!user) return;

        setIsProcessing(true);
        setError(null);
        setSuccessMessage(null);

        try {
            // In a real app, you would:
            // 1. Call your backend API to create a Stripe Checkout Session
            // 2. Redirect the user to the Stripe Checkout page
            // 3. Handle the webhook from Stripe to update the user's subscription status

            // For this demo, we'll simulate a successful subscription
            const now = new Date();
            const nextBillingDate = new Date(
                billingPeriod === 'monthly'
                    ? now.getTime() + 30 * 24 * 60 * 60 * 1000
                    : now.getTime() + 365 * 24 * 60 * 60 * 1000
            );

            const mockSubscription: Subscription = {
                id: 'sub_' + Math.random().toString(36).substring(2, 15),
                status: 'active',
                current_period_end: nextBillingDate.toISOString(),
                next_billing_date: nextBillingDate.toISOString(),
                cancel_at_period_end: false,
                plan: billingPeriod,
                created_at: now.toISOString(),
                start_date: now.toISOString(),
                price_id: billingPeriod === 'monthly' ? 'price_monthly' : 'price_annual'
            };

            // Create mock payment method
            const mockPaymentMethod: PaymentMethod = {
                id: 'pm_' + Math.random().toString(36).substring(2, 15),
                brand: 'visa',
                last4: '4242',
                exp_month: 12,
                exp_year: new Date().getFullYear() + 2,
                billing_details: {
                    name: user.user_metadata?.full_name || user.email,
                    email: user.email,
                    address: {
                        line1: '123 Main St',
                        city: 'San Francisco',
                        state: 'CA',
                        postal_code: '94105',
                        country: 'US'
                    }
                }
            };

            mockSubscription.payment_method = mockPaymentMethod;

            // Update the user's metadata to indicate they are a pro user
            await supabase.auth.updateUser({
                data: {
                    subscription: mockSubscription
                }
            });

            // In a real app, you would also store this in your database
            setSubscription(mockSubscription);
            setPaymentMethod(mockPaymentMethod);
            setSuccessMessage(`Successfully subscribed to Pro plan (${billingPeriod})!`);

            // Update usage data for Pro users
            setUsageData({
                decks: { total: 3, limit: Infinity, percentage: 0 },
                storage: { used: 25, limit: 10240, percentage: 0.2 },
                concepts: { total: 45, limit: Infinity, percentage: 0 },
                documents: { total: 2, limit: Infinity, percentage: 0 },
                ai_credits: {
                    used: 75,
                    limit: 1000,
                    percentage: 7.5,
                    reset_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                }
            });
        } catch (err: any) {
            console.error('Error creating subscription:', err);
            setError(err.message || 'Failed to create subscription');
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle subscription cancellation
    const handleCancelSubscription = async () => {
        if (!user || !subscription) return;

        setIsProcessing(true);
        setError(null);
        setSuccessMessage(null);

        try {
            // In a real app, you would:
            // 1. Call your backend API to cancel the subscription in Stripe
            // 2. Update your database with the cancellation status

            // For this demo, we'll simulate a successful cancellation
            const updatedSubscription = {
                ...subscription,
                cancel_at_period_end: true
            };

            // Update the user's metadata
            await supabase.auth.updateUser({
                data: {
                    subscription: updatedSubscription
                }
            });

            setSubscription(updatedSubscription);
            setSuccessMessage(
                'Your subscription will be canceled at the end of the billing period'
            );
        } catch (err: any) {
            console.error('Error canceling subscription:', err);
            setError(err.message || 'Failed to cancel subscription');
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle updating payment method
    const handleUpdatePaymentMethod = () => {
        // In a real app, this would open a Stripe modal to update the payment method
        setShowBillingDetails(true);
    };

    // Handle resuming a canceled subscription
    const handleResumeSubscription = async () => {
        if (!user || !subscription) return;

        setIsProcessing(true);
        setError(null);
        setSuccessMessage(null);

        try {
            // For this demo, we'll simulate resuming the subscription
            const updatedSubscription = {
                ...subscription,
                cancel_at_period_end: false
            };

            // Update the user's metadata
            await supabase.auth.updateUser({
                data: {
                    subscription: updatedSubscription
                }
            });

            setSubscription(updatedSubscription);
            setSuccessMessage('Your subscription has been resumed');
        } catch (err: any) {
            console.error('Error resuming subscription:', err);
            setError(err.message || 'Failed to resume subscription');
        } finally {
            setIsProcessing(false);
        }
    };

    // Handle changing billing period
    const handleChangeBillingPeriod = async () => {
        if (!user || !subscription) return;

        setIsProcessing(true);
        setError(null);
        setSuccessMessage(null);

        try {
            // For this demo, we'll simulate changing the billing period
            const newPeriod = subscription.plan === 'monthly' ? 'annual' : 'monthly';
            const now = new Date();
            const nextBillingDate = new Date(
                newPeriod === 'monthly'
                    ? now.getTime() + 30 * 24 * 60 * 60 * 1000
                    : now.getTime() + 365 * 24 * 60 * 60 * 1000
            );

            const updatedSubscription = {
                ...subscription,
                plan: newPeriod,
                price_id: newPeriod === 'monthly' ? 'price_monthly' : 'price_annual',
                current_period_end: nextBillingDate.toISOString(),
                next_billing_date: nextBillingDate.toISOString()
            };

            // Update the user's metadata
            await supabase.auth.updateUser({
                data: {
                    subscription: updatedSubscription
                }
            });

            setSubscription(updatedSubscription);
            setBillingPeriod(newPeriod as 'monthly' | 'annual');
            setSuccessMessage(`Successfully changed to ${newPeriod} billing`);
        } catch (err: any) {
            console.error('Error changing billing period:', err);
            setError(err.message || 'Failed to change billing period');
        } finally {
            setIsProcessing(false);
        }
    };

    if (!user) {
        return <div>Please log in to access billing settings.</div>;
    }

    return (
        <div className='mx-auto max-w-4xl p-6'>
            <h1 className='mb-6 text-2xl font-bold'>Billing</h1>

            {isLoading ? (
                <div className='flex justify-center p-8'>
                    <div className='h-8 w-8 animate-spin rounded-full border-b-2 border-primary'></div>
                </div>
            ) : (
                <>
                    {/* Current Subscription Status */}
                    {subscription && subscription.status === 'active' && (
                        <Card className='mb-8'>
                            <CardHeader>
                                <div className='flex items-center justify-between'>
                                    <div>
                                        <CardTitle>Current Subscription</CardTitle>
                                        <CardDescription>Manage your subscription</CardDescription>
                                    </div>
                                    <Badge className='bg-amber-500 hover:bg-amber-600'>
                                        <TbCrown className='mr-1 h-3.5 w-3.5' />
                                        Pro
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className='space-y-4'>
                                    <div>
                                        <p className='text-sm font-medium'>Status</p>
                                        <p className='text-sm text-gray-500'>
                                            {subscription.cancel_at_period_end
                                                ? 'Active (Cancels at end of billing period)'
                                                : 'Active'}
                                        </p>
                                    </div>
                                    <div>
                                        <p className='text-sm font-medium'>Current Period Ends</p>
                                        <p className='text-sm text-gray-500'>
                                            {formatDate(subscription.current_period_end)}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter>
                                {!subscription.cancel_at_period_end && (
                                    <Button
                                        variant='outline'
                                        onClick={handleCancelSubscription}
                                        disabled={isProcessing}
                                    >
                                        {isProcessing ? 'Processing...' : 'Cancel Subscription'}
                                    </Button>
                                )}
                            </CardFooter>
                        </Card>
                    )}

                    {/* Success/Error Messages */}
                    {successMessage && (
                        <Alert className='mb-6 border-green-200 bg-green-50'>
                            <AlertTitle>Success</AlertTitle>
                            <AlertDescription>{successMessage}</AlertDescription>
                        </Alert>
                    )}

                    {error && (
                        <Alert className='mb-6 border-red-200 bg-red-50'>
                            <AlertTitle>Error</AlertTitle>
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    {/* Pricing Plans */}
                    <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                        {/* Free Plan */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Free Plan</CardTitle>
                                <CardDescription>Basic features for personal use</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className='mb-4'>
                                    <span className='text-3xl font-bold'>$0</span>
                                    <span className='ml-1 text-gray-500'>/month</span>
                                </div>
                                <ul className='space-y-2'>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Up to 5 decks</span>
                                    </li>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Basic flashcard types</span>
                                    </li>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Standard spaced repetition</span>
                                    </li>
                                </ul>
                            </CardContent>
                            <CardFooter>
                                <Button variant='outline' disabled className='w-full'>
                                    Current Plan
                                </Button>
                            </CardFooter>
                        </Card>

                        {/* Pro Plan */}
                        <Card className='border-amber-200 shadow-md'>
                            <CardHeader className='rounded-t-lg bg-gradient-to-r from-amber-50 to-amber-100'>
                                <div className='flex items-center justify-between'>
                                    <CardTitle>Pro Plan</CardTitle>
                                    <Badge className='bg-amber-500 hover:bg-amber-600'>
                                        <TbCrown className='mr-1 h-3.5 w-3.5' />
                                        Recommended
                                    </Badge>
                                </div>
                                <CardDescription>Advanced features for power users</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className='mb-4'>
                                    <span className='text-3xl font-bold'>$9.99</span>
                                    <span className='ml-1 text-gray-500'>/month</span>
                                </div>
                                <ul className='space-y-2'>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Unlimited decks</span>
                                    </li>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>All flashcard types</span>
                                    </li>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Advanced FSRS algorithm</span>
                                    </li>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Priority support</span>
                                    </li>
                                    <li className='flex items-center'>
                                        <TbCheck className='mr-2 h-5 w-5 text-green-500' />
                                        <span className='text-sm'>Advanced analytics</span>
                                    </li>
                                </ul>
                            </CardContent>
                            <CardFooter>
                                {subscription?.status === 'active' ? (
                                    <Button variant='outline' disabled className='w-full'>
                                        Current Plan
                                    </Button>
                                ) : (
                                    <Button
                                        className='w-full bg-amber-500 hover:bg-amber-600'
                                        onClick={handleSubscribe}
                                        disabled={isProcessing}
                                    >
                                        {isProcessing ? 'Processing...' : 'Upgrade to Pro'}
                                    </Button>
                                )}
                            </CardFooter>
                        </Card>
                    </div>
                </>
            )}
        </div>
    );
}
