.DS_Store
node_modules
.turbo
*.log
dist
dist-ssr
*.local
.env
.cache
server/dist
public/dist
.env
!.env.example
.react-router
build
auto-imports.d.ts
*.tsbuildinfo

# Netlify specific
.netlify
*storybook.log

# Added by <PERSON> Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 
.qodo
