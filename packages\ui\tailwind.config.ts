import sharedConfig from '@repo/tailwind-config';
import type { Config } from 'tailwindcss';

export default {
    presets: [sharedConfig],
    content: [
        './ui/**/*.{js,jsx,ts,tsx}',
        './map/**/*.{js,jsx,ts,tsx}',
        './sidebar/**/*.{js,jsx,ts,tsx}',
        './floatingmenu/**/*.{js,jsx,ts,tsx}',
        './review/**/*.{js,jsx,ts,tsx}',
        './pdf/**/*.{js,jsx,ts,tsx}'
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: [
                    '"Inter"',
                    'ui-sans-serif',
                    'system-ui',
                    'sans-serif',
                    '"Apple Color Emoji"',
                    '"Segoe UI Emoji"',
                    '"Segoe UI Symbol"',
                    '"Noto Color Emoji"'
                ]
            }
        }
    }
} satisfies Config;
